# app/core/error_handler.py
from typing import Dict, Any, Optional, List
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import logging
import traceback
from datetime import datetime

logger = logging.getLogger(__name__)

class ErrorDetail(BaseModel):
    """Standardized error detail"""
    code: str
    message: str
    field: Optional[str] = None

class StandardErrorResponse(BaseModel):
    """Standardized error response format"""
    success: bool = False
    error: str
    error_code: str
    details: Optional[List[ErrorDetail]] = None
    timestamp: str
    request_id: Optional[str] = None
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "error": "Invalid API request",
                "error_code": "VALIDATION_ERROR",
                "details": [
                    {
                        "code": "INVALID_FIELD",
                        "message": "The field 'coordinates' is required",
                        "field": "coordinates"
                    }
                ],
                "timestamp": "2025-08-30T15:30:00Z",
                "request_id": "req_123456789"
            }
        }

class ErrorCodes:
    """Standardized error codes"""
    # Validation errors
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INVALID_INPUT = "INVALID_INPUT"
    MISSING_FIELD = "MISSING_FIELD"
    INVALID_FORMAT = "INVALID_FORMAT"
    
    # Authentication/Authorization
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    INVALID_API_KEY = "INVALID_API_KEY"
    
    # API errors
    API_ERROR = "API_ERROR"
    API_UNAVAILABLE = "API_UNAVAILABLE"
    API_RATE_LIMIT = "API_RATE_LIMIT"
    API_TIMEOUT = "API_TIMEOUT"
    
    # System errors
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    DATABASE_ERROR = "DATABASE_ERROR"
    
    # Business logic errors
    NOT_FOUND = "NOT_FOUND"
    ALREADY_EXISTS = "ALREADY_EXISTS"
    INSUFFICIENT_QUOTA = "INSUFFICIENT_QUOTA"

def create_error_response(
    error_message: str,
    error_code: str = ErrorCodes.INTERNAL_ERROR,
    status_code: int = 500,
    details: Optional[List[ErrorDetail]] = None,
    request_id: Optional[str] = None
) -> JSONResponse:
    """Create standardized error response"""
    
    error_response = StandardErrorResponse(
        error=error_message,
        error_code=error_code,
        details=details,
        timestamp=datetime.utcnow().isoformat() + "Z",
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=status_code,
        content=error_response.dict()
    )

def map_http_status_to_error_code(status_code: int) -> str:
    """Map HTTP status codes to standardized error codes"""
    mapping = {
        400: ErrorCodes.VALIDATION_ERROR,
        401: ErrorCodes.UNAUTHORIZED,
        403: ErrorCodes.FORBIDDEN,
        404: ErrorCodes.NOT_FOUND,
        409: ErrorCodes.ALREADY_EXISTS,
        429: ErrorCodes.API_RATE_LIMIT,
        500: ErrorCodes.INTERNAL_ERROR,
        502: ErrorCodes.API_UNAVAILABLE,
        503: ErrorCodes.SERVICE_UNAVAILABLE,
        504: ErrorCodes.API_TIMEOUT
    }
    return mapping.get(status_code, ErrorCodes.INTERNAL_ERROR)

async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Global exception handler for unhandled exceptions"""
    
    # Generate request ID for tracking
    request_id = getattr(request.state, 'request_id', None)
    
    if isinstance(exc, HTTPException):
        error_code = map_http_status_to_error_code(exc.status_code)
        return create_error_response(
            error_message=exc.detail,
            error_code=error_code,
            status_code=exc.status_code,
            request_id=request_id
        )
    
    # Log unexpected errors
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    
    return create_error_response(
        error_message="An unexpected error occurred",
        error_code=ErrorCodes.INTERNAL_ERROR,
        status_code=500,
        request_id=request_id
    )

def handle_api_error(
    api_name: str,
    error_message: str,
    status_code: Optional[int] = None,
    original_error: Optional[Exception] = None
) -> HTTPException:
    """Handle API-specific errors with consistent formatting"""
    
    # Map common API errors to appropriate HTTP status codes
    if status_code is None:
        if "timeout" in error_message.lower():
            status_code = 504
        elif "rate limit" in error_message.lower():
            status_code = 429
        elif "unauthorized" in error_message.lower() or "invalid key" in error_message.lower():
            status_code = 401
        elif "not found" in error_message.lower():
            status_code = 404
        else:
            status_code = 502  # Bad Gateway for external API errors
    
    error_code = map_http_status_to_error_code(status_code)
    
    # Create detailed error message
    detailed_message = f"{api_name} API error: {error_message}"
    
    # Log the error
    if original_error:
        logger.error(f"{detailed_message}", exc_info=True)
    else:
        logger.error(detailed_message)
    
    raise HTTPException(
        status_code=status_code,
        detail=detailed_message
    )

class APIErrorHandler:
    """Context manager for handling API errors consistently"""
    
    def __init__(self, api_name: str, operation: str):
        self.api_name = api_name
        self.operation = operation
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            # Handle different types of exceptions
            if isinstance(exc_val, HTTPException):
                # Re-raise HTTP exceptions as-is
                return False
            elif isinstance(exc_val, (ConnectionError, TimeoutError)):
                handle_api_error(
                    self.api_name,
                    f"Connection error during {self.operation}: {str(exc_val)}",
                    504,
                    exc_val
                )
            else:
                handle_api_error(
                    self.api_name,
                    f"Unexpected error during {self.operation}: {str(exc_val)}",
                    500,
                    exc_val
                )
        return False