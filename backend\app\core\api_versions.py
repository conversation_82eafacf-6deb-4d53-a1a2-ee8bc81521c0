# app/core/api_versions.py
import os
from typing import Dict, Optional
from app.core.env_config import EnvironmentConfig

class APIVersionManager:
    """Centralized API version management"""
    
    # Default API versions
    DEFAULT_VERSIONS = {
        # Google APIs
        'google_places': 'v1',
        'google_maps': 'v3',
        'google_geocoding': 'v1',
        
        # Amadeus API
        'amadeus': 'v2',
        'amadeus_auth': 'v1',
        
        # Weather APIs
        'openweather': '2.5',
        'tomorrow_io': 'v4',
        
        # Travel APIs
        'hotelbeds': '1.0',
        'geoapify': 'v1',
        
        # Flight APIs
        'flightapi': 'v1',
        'amadeus_flights': 'v2',
        
        # Other services
        'vertex_ai': 'v1',
        'speech_to_text': 'v1',
    }
    
    @classmethod
    def get_api_version(cls, api_name: str, default_version: Optional[str] = None) -> str:
        """
        Get API version for a specific API
        
        Args:
            api_name: Name of the API
            default_version: Override default version
            
        Returns:
            API version string
        """
        # Try environment variable first
        env_var = f"{api_name.upper()}_API_VERSION"
        version = EnvironmentConfig.get_env_var(env_var)
        
        if version:
            return version
        
        # Use provided default or system default
        if default_version:
            return default_version
        
        return cls.DEFAULT_VERSIONS.get(api_name.lower(), 'v1')
    
    @classmethod
    def get_versioned_url(cls, base_url: str, api_name: str, endpoint: str = "", version: Optional[str] = None) -> str:
        """
        Build versioned URL for API endpoint
        
        Args:
            base_url: Base URL of the API
            api_name: Name of the API for version lookup
            endpoint: Specific endpoint path
            version: Override version
            
        Returns:
            Complete versioned URL
        """
        api_version = version or cls.get_api_version(api_name)
        
        # Handle different URL patterns
        if base_url.endswith('/'):
            base_url = base_url.rstrip('/')
        
        if endpoint.startswith('/'):
            endpoint = endpoint[1:]
        
        # Build URL based on common patterns
        if api_name.lower() in ['google_places', 'google_maps', 'google_geocoding']:
            # Google APIs: https://maps.googleapis.com/maps/api/place/v1/details
            return f"{base_url}/{api_version}/{endpoint}" if endpoint else f"{base_url}/{api_version}"
        
        elif api_name.lower().startswith('amadeus'):
            # Amadeus APIs: https://api.amadeus.com/v2/shopping/flight-offers
            return f"{base_url}/{api_version}/{endpoint}" if endpoint else f"{base_url}/{api_version}"
        
        elif api_name.lower() in ['openweather']:
            # OpenWeather: https://api.openweathermap.org/data/2.5/weather
            return f"{base_url}/{api_version}/{endpoint}" if endpoint else f"{base_url}/{api_version}"
        
        elif api_name.lower() in ['tomorrow_io']:
            # Tomorrow.io: https://api.tomorrow.io/v4/weather/forecast
            return f"{base_url}/{api_version}/{endpoint}" if endpoint else f"{base_url}/{api_version}"
        
        else:
            # Generic pattern: base_url/version/endpoint
            return f"{base_url}/{api_version}/{endpoint}" if endpoint else f"{base_url}/{api_version}"
    
    @classmethod
    def get_all_versions(cls) -> Dict[str, str]:
        """Get all configured API versions"""
        versions = {}
        for api_name in cls.DEFAULT_VERSIONS.keys():
            versions[api_name] = cls.get_api_version(api_name)
        return versions
    
    @classmethod
    def update_version(cls, api_name: str, version: str) -> None:
        """
        Update API version (for runtime configuration)
        Note: This sets environment variable for current session
        """
        env_var = f"{api_name.upper()}_API_VERSION"
        os.environ[env_var] = version
    
    @classmethod
    def get_version_info(cls) -> Dict[str, Dict[str, str]]:
        """Get comprehensive version information"""
        info = {
            'configured_versions': cls.get_all_versions(),
            'default_versions': cls.DEFAULT_VERSIONS.copy(),
            'environment_overrides': {}
        }
        
        # Check for environment overrides
        for api_name in cls.DEFAULT_VERSIONS.keys():
            env_var = f"{api_name.upper()}_API_VERSION"
            env_value = os.getenv(env_var)
            if env_value:
                info['environment_overrides'][api_name] = env_value
        
        return info

# Convenience functions for common APIs
def get_google_places_url(endpoint: str = "") -> str:
    """Get versioned Google Places API URL"""
    base_url = "https://maps.googleapis.com/maps/api/place"
    return APIVersionManager.get_versioned_url(base_url, "google_places", endpoint)

def get_amadeus_url(endpoint: str = "") -> str:
    """Get versioned Amadeus API URL"""
    base_url = "https://api.amadeus.com"
    return APIVersionManager.get_versioned_url(base_url, "amadeus", endpoint)

def get_openweather_url(endpoint: str = "") -> str:
    """Get versioned OpenWeather API URL"""
    base_url = "https://api.openweathermap.org/data"
    return APIVersionManager.get_versioned_url(base_url, "openweather", endpoint)

def get_tomorrow_io_url(endpoint: str = "") -> str:
    """Get versioned Tomorrow.io API URL"""
    base_url = "https://api.tomorrow.io"
    return APIVersionManager.get_versioned_url(base_url, "tomorrow_io", endpoint)

def get_geoapify_url(endpoint: str = "") -> str:
    """Get versioned Geoapify API URL"""
    base_url = "https://api.geoapify.com"
    return APIVersionManager.get_versioned_url(base_url, "geoapify", endpoint)

# Version validation utilities
def validate_version_format(version: str) -> bool:
    """Validate API version format"""
    import re
    # Common version patterns: v1, v2.1, 2.5, 1.0, etc.
    patterns = [
        r'^v\d+(\.\d+)*$',  # v1, v2.1, v1.2.3
        r'^\d+(\.\d+)*$',   # 1, 2.5, 1.2.3
    ]
    
    return any(re.match(pattern, version) for pattern in patterns)

def get_latest_supported_version(api_name: str) -> str:
    """Get the latest supported version for an API"""
    # This could be extended to check with actual API documentation
    # For now, return the configured version
    return APIVersionManager.get_api_version(api_name)