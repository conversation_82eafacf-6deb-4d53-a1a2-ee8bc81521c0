# app/services/amadeus_service.py
import os
import httpx
import logging
from typing import Optional, Dict, Any
from fastapi import HTTPException
import json
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Amadeus API Configuration
AMADEUS_BASE_URL = "https://test.api.amadeus.com"
AMADEUS_TOKEN_URL = f"{AMADEUS_BASE_URL}/v1/security/oauth2/token"

def get_amadeus_credentials():
    """Get Amadeus API credentials from environment variables"""
    api_key = os.getenv('AMADEUS_API_KEY')  # Using the typo to match .env file
    api_secret = os.getenv('AMADEUS_API_SECRET')
    
    if not api_key or not api_secret:
        raise ValueError("AMADEUS_API_KEY and AMADEUS_API_SECRET environment variables must be set")
    
    return api_key, api_secret

class AmadeusTokenManager:
    """Manages Amadeus API access tokens with automatic refresh"""
    
    def __init__(self):
        self.access_token = None
        self.token_expires_at = None
        self.api_key, self.api_secret = get_amadeus_credentials()
    
    async def get_access_token(self) -> str:
        """Get a valid access token, refreshing if necessary"""
        if self.access_token and self.token_expires_at:
            # Check if token is still valid (with 5 minute buffer)
            if datetime.now() < self.token_expires_at - timedelta(minutes=5):
                return self.access_token
        
        # Token is expired or doesn't exist, get a new one
        await self._refresh_token()
        return self.access_token
    
    async def _refresh_token(self):
        """Refresh the access token"""
        try:
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            data = {
                "grant_type": "client_credentials",
                "client_id": self.api_key,
                "client_secret": self.api_secret
            }
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(AMADEUS_TOKEN_URL, headers=headers, data=data)
                
                if response.status_code == 200:
                    token_data = response.json()
                    self.access_token = token_data["access_token"]
                    expires_in = token_data.get("expires_in", 1799)  # Default to ~30 minutes
                    self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                    
                    logger.info("Amadeus access token refreshed successfully")
                else:
                    logger.error(f"Failed to refresh Amadeus token: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Failed to authenticate with Amadeus API: {response.text}"
                    )
                    
        except httpx.RequestError as e:
            logger.error(f"Request error during token refresh: {str(e)}")
            raise HTTPException(status_code=503, detail="Amadeus authentication service unavailable")
        except Exception as e:
            logger.error(f"Unexpected error during token refresh: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal authentication error")

# Global token manager instance
token_manager = AmadeusTokenManager()

async def amadeus_request(endpoint: str, params: Optional[Dict[str, Any]] = None, method: str = "GET", data: Optional[Dict[str, Any]] = None) -> dict:
    """Generic Amadeus API request with authentication and error handling"""
    try:
        # Get valid access token
        access_token = await token_manager.get_access_token()
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }
        
        # Add content-type for POST requests
        if method.upper() == "POST" and data:
            headers["Content-Type"] = "application/json"
        
        url = f"{AMADEUS_BASE_URL}{endpoint}"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info(f"Making {method} request to: {url}")
            
            if method.upper() == "GET":
                response = await client.get(url, headers=headers, params=params or {})
            elif method.upper() == "POST":
                response = await client.post(url, headers=headers, params=params or {}, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 204:
                return {"message": "No content found"}
            elif response.status_code == 400:
                error_detail = response.text
                try:
                    error_json = response.json()
                    if "errors" in error_json:
                        error_detail = error_json["errors"][0].get("detail", error_detail)
                except:
                    pass
                logger.error(f"Bad request to Amadeus API: {error_detail}")
                raise HTTPException(status_code=400, detail=f"Invalid request: {error_detail}")
            elif response.status_code == 401:
                # Token might be invalid, try to refresh
                logger.warning("Amadeus API returned 401, attempting token refresh")
                await token_manager._refresh_token()
                # Retry the request once with new token
                headers["Authorization"] = f"Bearer {token_manager.access_token}"
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params or {})
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, params=params or {}, json=data)
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"Amadeus API authentication failed after retry: {response.status_code}")
                    raise HTTPException(status_code=401, detail="Authentication failed")
            else:
                logger.error(f"Amadeus API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Amadeus API error: {response.text}"
                )
                
    except httpx.TimeoutException:
        logger.error("Request timeout to Amadeus API")
        raise HTTPException(status_code=408, detail="Request timeout")
    except httpx.RequestError as e:
        logger.error(f"Request error: {str(e)}")
        raise HTTPException(status_code=503, detail="Amadeus API service unavailable")
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Flight Offers Search API functions
async def search_flight_offers(
    origin_location_code: str,
    destination_location_code: str,
    departure_date: str,
    return_date: Optional[str] = None,
    adults: int = 1,
    children: Optional[int] = None,
    infants: Optional[int] = None,
    travel_class: Optional[str] = None,
    included_airline_codes: Optional[str] = None,
    excluded_airline_codes: Optional[str] = None,
    non_stop: Optional[bool] = None,
    currency_code: str = "USD",
    max_price: Optional[int] = None,
    max_results: Optional[int] = None
) -> dict:
    """Search for flight offers using GET method"""
    params = {
        "originLocationCode": origin_location_code,
        "destinationLocationCode": destination_location_code,
        "departureDate": departure_date,
        "adults": adults,
        "currencyCode": currency_code
    }
    
    # Add optional parameters
    if return_date:
        params["returnDate"] = return_date
    if children:
        params["children"] = children
    if infants:
        params["infants"] = infants
    if travel_class:
        params["travelClass"] = travel_class
    if included_airline_codes:
        params["includedAirlineCodes"] = included_airline_codes
    if excluded_airline_codes:
        params["excludedAirlineCodes"] = excluded_airline_codes
    if non_stop is not None:
        params["nonStop"] = str(non_stop).lower()
    if max_price:
        params["maxPrice"] = max_price
    if max_results:
        params["max"] = max_results
    
    return await amadeus_request("/v2/shopping/flight-offers", params=params)

async def search_flight_offers_post(request_data: dict) -> dict:
    """Search for flight offers using POST method with full functionality"""
    return await amadeus_request(
        "/v2/shopping/flight-offers",
        method="POST",
        data=request_data
    )

# Flight Status API functions
async def get_flight_status(
    carrier_code: str,
    flight_number: str,
    scheduled_departure_date: str,
    operational_suffix: Optional[str] = None
) -> dict:
    """Get real-time flight status information"""
    params = {
        "carrierCode": carrier_code,
        "flightNumber": flight_number,
        "scheduledDepartureDate": scheduled_departure_date
    }
    
    if operational_suffix:
        params["operationalSuffix"] = operational_suffix
    
    return await amadeus_request("/v2/schedule/flights", params=params)

# Flight Cheapest Date Search API functions
async def search_cheapest_flight_dates(
    origin: str,
    destination: Optional[str] = None,
    departure_date: Optional[str] = None,
    one_way: Optional[bool] = None,
    duration: Optional[int] = None,
    non_stop: Optional[bool] = None,
    max_price: Optional[float] = None,
    view_by: Optional[str] = None
) -> dict:
    """Search for cheapest flight dates"""
    params = {
        "origin": origin
    }
    
    # Add optional parameters
    if destination:
        params["destination"] = destination
    if departure_date:
        params["departureDate"] = departure_date
    if one_way is not None:
        params["oneWay"] = str(one_way).lower()
    if duration:
        params["duration"] = duration
    if non_stop is not None:
        params["nonStop"] = str(non_stop).lower()
    if max_price:
        params["maxPrice"] = max_price
    if view_by:
        params["viewBy"] = view_by
    
    return await amadeus_request("/v1/shopping/flight-dates", params=params)

# Airport and Airline lookup functions
async def search_airports(keyword: str, subtype: Optional[str] = None) -> dict:
    """Search for airports by keyword"""
    params = {
        "keyword": keyword,
        "subType": subtype or "AIRPORT"
    }
    
    return await amadeus_request("/v1/reference-data/locations", params=params)

async def get_airline_routes(
    origin_iata_code: str,
    destination_iata_code: Optional[str] = None
) -> dict:
    """Get airline routes information"""
    params = {
        "originIataCode": origin_iata_code
    }
    
    if destination_iata_code:
        params["destinationIataCode"] = destination_iata_code
    
    return await amadeus_request("/v1/airline/destinations", params=params)

# Utility functions
def validate_date_format(date_string: str) -> bool:
    """Validate date string is in YYYY-MM-DD format"""
    try:
        datetime.strptime(date_string, "%Y-%m-%d")
        return True
    except ValueError:
        return False

def validate_iata_code(code: str) -> bool:
    """Validate IATA code format (3 letters)"""
    return len(code) == 3 and code.isalpha() and code.isupper()

def format_flight_duration(duration_str: str) -> str:
    """Format ISO 8601 duration string to human readable format"""
    try:
        # Parse ISO 8601 duration (e.g., "PT2H5M" -> "2h 5m")
        if duration_str.startswith("PT"):
            duration_str = duration_str[2:]  # Remove "PT" prefix
            
            hours = 0
            minutes = 0
            
            if "H" in duration_str:
                hours_part, duration_str = duration_str.split("H")
                hours = int(hours_part)
            
            if "M" in duration_str:
                minutes_part = duration_str.split("M")[0]
                minutes = int(minutes_part)
            
            if hours > 0 and minutes > 0:
                return f"{hours}h {minutes}m"
            elif hours > 0:
                return f"{hours}h"
            elif minutes > 0:
                return f"{minutes}m"
            else:
                return "0m"
    except:
        return duration_str  # Return original if parsing fails
    
    return duration_str