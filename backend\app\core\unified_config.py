# app/core/unified_config.py
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from app.core.env_config import EnvironmentConfig
from app.core.api_versions import APIVersionManager
from app.core.user_agent import UserAgentManager

@dataclass
class APIEndpointConfig:
    """Configuration for a single API endpoint"""
    name: str
    base_url: str
    version: str
    timeout: int = 30
    rate_limit: int = 100
    cache_ttl: int = 300
    retry_count: int = 3
    retry_delay: float = 1.0
    user_agent: Optional[str] = None
    headers: Dict[str, str] = field(default_factory=dict)
    auth_type: str = "api_key"  # api_key, bearer, basic, custom
    required_credentials: List[str] = field(default_factory=list)
    
    def get_full_url(self, endpoint: str = "") -> str:
        """Get full URL with version"""
        return APIVersionManager.get_versioned_url(self.base_url, self.name, endpoint, self.version)
    
    def get_headers(self) -> Dict[str, str]:
        """Get complete headers including User-Agent"""
        headers = UserAgentManager.get_api_specific_headers(self.name)
        headers.update(self.headers)
        if self.user_agent:
            headers['User-Agent'] = self.user_agent
        return headers

class UnifiedConfigManager:
    """Centralized configuration manager for all APIs"""
    
    def __init__(self):
        self._configs: Dict[str, APIEndpointConfig] = {}
        self._load_default_configs()
    
    def _load_default_configs(self):
        """Load default configurations for all APIs"""
        
        # Google Places API
        self._configs['google_places'] = APIEndpointConfig(
            name='google_places',
            base_url='https://maps.googleapis.com/maps/api/place',
            version=APIVersionManager.get_api_version('google_places'),
            timeout=EnvironmentConfig.get_int_env_var('GOOGLE_PLACES_TIMEOUT_SECONDS', 30),
            rate_limit=EnvironmentConfig.get_int_env_var('GOOGLE_PLACES_RATE_LIMIT', 100),
            cache_ttl=3600,  # 1 hour for place data
            required_credentials=['api_key'],
            headers={'Accept': 'application/json'}
        )
        
        # Amadeus API
        self._configs['amadeus'] = APIEndpointConfig(
            name='amadeus',
            base_url='https://api.amadeus.com',
            version=APIVersionManager.get_api_version('amadeus'),
            timeout=EnvironmentConfig.get_int_env_var('AMADEUS_TIMEOUT_SECONDS', 30),
            rate_limit=EnvironmentConfig.get_int_env_var('AMADEUS_RATE_LIMIT', 50),
            cache_ttl=1800,  # 30 minutes for travel data
            auth_type='bearer',
            required_credentials=['api_key', 'api_secret'],
            headers={'Accept': 'application/vnd.amadeus+json'}
        )
        
        # FlightAPI
        self._configs['flightapi'] = APIEndpointConfig(
            name='flightapi',
            base_url='https://api.flightapi.io',
            version=APIVersionManager.get_api_version('flightapi'),
            timeout=EnvironmentConfig.get_int_env_var('FLIGHT_API_TIMEOUT_SECONDS', 60),
            rate_limit=EnvironmentConfig.get_int_env_var('FLIGHT_API_RATE_LIMIT', 50),
            cache_ttl=300,  # 5 minutes for flight prices
            required_credentials=['api_key']
        )
        
        # OpenWeatherMap API
        self._configs['openweather'] = APIEndpointConfig(
            name='openweather',
            base_url='https://api.openweathermap.org/data',
            version=APIVersionManager.get_api_version('openweather'),
            timeout=EnvironmentConfig.get_int_env_var('OPENWEATHER_TIMEOUT_SECONDS', 30),
            rate_limit=EnvironmentConfig.get_int_env_var('OPENWEATHER_RATE_LIMIT', 60),
            cache_ttl=600,  # 10 minutes for weather data
            required_credentials=['api_key']
        )
        
        # Tomorrow.io API
        self._configs['tomorrow_io'] = APIEndpointConfig(
            name='tomorrow_io',
            base_url='https://api.tomorrow.io',
            version=APIVersionManager.get_api_version('tomorrow_io'),
            timeout=EnvironmentConfig.get_int_env_var('TOMORROW_IO_TIMEOUT_SECONDS', 30),
            rate_limit=EnvironmentConfig.get_int_env_var('TOMORROW_IO_RATE_LIMIT', 100),
            cache_ttl=600,  # 10 minutes for weather data
            required_credentials=['api_key']
        )
        
        # Hotelbeds API
        self._configs['hotelbeds'] = APIEndpointConfig(
            name='hotelbeds',
            base_url='https://api.test.hotelbeds.com',
            version=APIVersionManager.get_api_version('hotelbeds'),
            timeout=EnvironmentConfig.get_int_env_var('HOTELBEDS_TIMEOUT_SECONDS', 30),
            rate_limit=EnvironmentConfig.get_int_env_var('HOTELBEDS_RATE_LIMIT', 100),
            cache_ttl=3600,  # 1 hour for hotel data
            auth_type='custom',
            required_credentials=['api_key', 'secret'],
            headers={'Accept': 'application/json', 'Accept-Encoding': 'gzip'}
        )
        
        # Geoapify API
        self._configs['geoapify'] = APIEndpointConfig(
            name='geoapify',
            base_url='https://api.geoapify.com',
            version=APIVersionManager.get_api_version('geoapify'),
            timeout=EnvironmentConfig.get_int_env_var('GEOAPIFY_TIMEOUT_SECONDS', 30),
            rate_limit=EnvironmentConfig.get_int_env_var('GEOAPIFY_RATE_LIMIT', 1000),
            cache_ttl=3600,  # 1 hour for geocoding data
            required_credentials=['api_key']
        )
    
    def get_config(self, api_name: str) -> Optional[APIEndpointConfig]:
        """Get configuration for specific API"""
        return self._configs.get(api_name.lower())
    
    def get_all_configs(self) -> Dict[str, APIEndpointConfig]:
        """Get all API configurations"""
        return self._configs.copy()
    
    def update_config(self, api_name: str, **kwargs):
        """Update configuration for specific API"""
        if api_name.lower() in self._configs:
            config = self._configs[api_name.lower()]
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
    
    def add_custom_config(self, config: APIEndpointConfig):
        """Add custom API configuration"""
        self._configs[config.name.lower()] = config
    
    def validate_credentials(self, api_name: str) -> Dict[str, bool]:
        """Validate that required credentials are available"""
        config = self.get_config(api_name)
        if not config:
            return {'error': f'No configuration found for {api_name}'}
        
        credentials = EnvironmentConfig.get_api_credentials(api_name)
        validation_results = {}
        
        for cred in config.required_credentials:
            validation_results[cred] = credentials.get(cred) is not None
        
        return validation_results
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get summary of all configurations"""
        summary = {
            'total_apis': len(self._configs),
            'apis': {},
            'global_settings': {
                'default_timeout': EnvironmentConfig.get_int_env_var('API_TIMEOUT_SECONDS', 30),
                'default_rate_limit': EnvironmentConfig.get_int_env_var('DEFAULT_RATE_LIMIT', 100),
                'default_cache_ttl': EnvironmentConfig.get_int_env_var('DEFAULT_CACHE_TTL', 300),
            }
        }
        
        for api_name, config in self._configs.items():
            credentials_valid = self.validate_credentials(api_name)
            
            summary['apis'][api_name] = {
                'base_url': config.base_url,
                'version': config.version,
                'timeout': config.timeout,
                'rate_limit': config.rate_limit,
                'cache_ttl': config.cache_ttl,
                'auth_type': config.auth_type,
                'credentials_configured': all(credentials_valid.values()) if isinstance(credentials_valid, dict) else False,
                'required_credentials': config.required_credentials
            }
        
        return summary
    
    def export_config(self, format: str = 'dict') -> Any:
        """Export configuration in specified format"""
        if format == 'dict':
            return {name: {
                'name': config.name,
                'base_url': config.base_url,
                'version': config.version,
                'timeout': config.timeout,
                'rate_limit': config.rate_limit,
                'cache_ttl': config.cache_ttl,
                'retry_count': config.retry_count,
                'retry_delay': config.retry_delay,
                'auth_type': config.auth_type,
                'required_credentials': config.required_credentials,
                'headers': config.headers
            } for name, config in self._configs.items()}
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def load_config_from_file(self, file_path: str):
        """Load configuration from JSON file"""
        import json
        try:
            with open(file_path, 'r') as f:
                config_data = json.load(f)
            
            for api_name, api_config in config_data.items():
                config = APIEndpointConfig(**api_config)
                self._configs[api_name.lower()] = config
                
        except Exception as e:
            raise ValueError(f"Failed to load configuration from {file_path}: {e}")
    
    def save_config_to_file(self, file_path: str):
        """Save current configuration to JSON file"""
        import json
        try:
            config_data = self.export_config('dict')
            with open(file_path, 'w') as f:
                json.dump(config_data, f, indent=2)
        except Exception as e:
            raise ValueError(f"Failed to save configuration to {file_path}: {e}")

# Global unified configuration manager
unified_config = UnifiedConfigManager()

# Convenience functions
def get_api_config(api_name: str) -> Optional[APIEndpointConfig]:
    """Get configuration for specific API"""
    return unified_config.get_config(api_name)

def get_api_url(api_name: str, endpoint: str = "") -> str:
    """Get full URL for API endpoint"""
    config = get_api_config(api_name)
    if not config:
        raise ValueError(f"No configuration found for API: {api_name}")
    return config.get_full_url(endpoint)

def get_api_headers(api_name: str) -> Dict[str, str]:
    """Get headers for API"""
    config = get_api_config(api_name)
    if not config:
        raise ValueError(f"No configuration found for API: {api_name}")
    return config.get_headers()

def get_api_timeout(api_name: str) -> int:
    """Get timeout for API"""
    config = get_api_config(api_name)
    return config.timeout if config else 30

def get_api_rate_limit(api_name: str) -> int:
    """Get rate limit for API"""
    config = get_api_config(api_name)
    return config.rate_limit if config else 100

def get_api_cache_ttl(api_name: str) -> int:
    """Get cache TTL for API"""
    config = get_api_config(api_name)
    return config.cache_ttl if config else 300

def validate_all_api_credentials() -> Dict[str, Dict[str, bool]]:
    """Validate credentials for all APIs"""
    results = {}
    for api_name in unified_config.get_all_configs().keys():
        results[api_name] = unified_config.validate_credentials(api_name)
    return results