#!/usr/bin/env python3
"""
Phase 2 Integration Test
Tests rate limiting and caching functionality
"""

import asyncio
import time
from app.core.cache_manager import cached_api_call, cache_manager

# Test caching decorator
@cached_api_call("test_api", "test_endpoint", ttl=60)
async def test_cached_function(param1: str, param2: int):
    """Test function with caching"""
    # Simulate API call delay
    await asyncio.sleep(0.1)
    return {
        "success": True,
        "data": f"Result for {param1} with {param2}",
        "timestamp": time.time()
    }

async def test_caching_performance():
    """Test caching performance and functionality"""
    print("🧪 Testing Caching Performance...")
    
    # First call (should be slow - cache miss)
    start_time = time.time()
    result1 = await test_cached_function("test", 123)
    first_call_time = time.time() - start_time
    
    # Second call (should be fast - cache hit)
    start_time = time.time()
    result2 = await test_cached_function("test", 123)
    second_call_time = time.time() - start_time
    
    print(f"📊 First call (cache miss): {first_call_time:.3f}s")
    print(f"📊 Second call (cache hit): {second_call_time:.3f}s")
    
    if second_call_time > 0:
        improvement = first_call_time / second_call_time
        print(f"🚀 Speed improvement: {improvement:.1f}x faster")
    else:
        print("🚀 Speed improvement: Instant (cache hit)")
    
    # Verify results are identical
    assert result1["data"] == result2["data"], "Cached results should be identical"
    print("✅ Cache functionality verified")
    
    # Check cache statistics
    stats = await cache_manager.get_cache_stats()
    print(f"📈 Cache stats: {stats}")
    
    return first_call_time, second_call_time

async def test_rate_limiter_import():
    """Test rate limiter can be imported and initialized"""
    print("🧪 Testing Rate Limiter...")
    
    try:
        from app.middleware.rate_limiter import RateLimitMiddleware, RateLimiter
        
        # Test rate limiter creation
        limiter = RateLimiter(max_requests=10, time_window=60)
        
        # Test multiple requests
        allowed_count = 0
        for i in range(15):  # Try 15 requests (limit is 10)
            if await limiter.is_allowed("test_client"):
                allowed_count += 1
        
        print(f"📊 Rate limiter allowed {allowed_count}/15 requests (limit: 10)")
        assert allowed_count == 10, f"Expected 10 allowed requests, got {allowed_count}"
        print("✅ Rate limiter functionality verified")
        
    except Exception as e:
        print(f"❌ Rate limiter test failed: {e}")
        raise

async def main():
    """Run all Phase 2 integration tests"""
    print("🚀 Starting Phase 2 Integration Tests")
    print("=" * 50)
    
    try:
        # Test caching
        first_time, second_time = await test_caching_performance()
        
        print()
        
        # Test rate limiting
        await test_rate_limiter_import()
        
        print()
        print("🎯 Phase 2 Integration Tests Complete!")
        print("=" * 50)
        print("✅ All systems operational:")
        if second_time > 0:
            print(f"   • Caching: {second_time/first_time*100:.1f}% of original time")
        else:
            print("   • Caching: Instant cache hits")
        print("   • Rate Limiting: Functional")
        print("   • Health Monitoring: Enhanced with cache stats")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())