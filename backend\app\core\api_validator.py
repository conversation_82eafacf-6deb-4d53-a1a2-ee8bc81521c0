# app/core/api_validator.py
import os
import asyncio
import aiohttp
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)

class APIValidator:
    """Centralized API key validation system"""
    
    def __init__(self):
        self.validation_results = {}
    
    async def validate_all_apis(self) -> Dict[str, bool]:
        """Validate all API keys at startup"""
        validators = [
            self._validate_amadeus(),
            self._validate_hotelbeds(),
            self._validate_geoapify(),
            self._validate_google_places(),
            self._validate_openweather(),
            self._validate_tomorrow_io(),
            self._validate_flight_api()
        ]
        
        results = await asyncio.gather(*validators, return_exceptions=True)
        
        api_names = [
            "amadeus", "hotelbeds", "geoapify", "google_places", 
            "openweather", "tomorrow_io", "flight_api"
        ]
        
        for i, result in enumerate(results):
            api_name = api_names[i]
            if isinstance(result, Exception):
                logger.error(f"API validation failed for {api_name}: {result}")
                self.validation_results[api_name] = False
            else:
                self.validation_results[api_name] = result
                logger.info(f"API validation for {api_name}: {'✅' if result else '❌'}")
        
        return self.validation_results
    
    async def _validate_amadeus(self) -> bool:
        """Validate Amadeus API credentials"""
        try:
            api_key = os.getenv('AMADEUS_API_KEY')
            api_secret = os.getenv('AMADEUS_API_SECRET')
            
            if not api_key or not api_secret:
                return False
            
            # Test token endpoint
            async with aiohttp.ClientSession() as session:
                data = {
                    'grant_type': 'client_credentials',
                    'client_id': api_key,
                    'client_secret': api_secret
                }
                
                async with session.post(
                    "https://test.api.amadeus.com/v1/security/oauth2/token",
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Amadeus validation error: {e}")
            return False
    
    async def _validate_hotelbeds(self) -> bool:
        """Validate HotelBeds API credentials"""
        try:
            api_key = os.getenv('HOTELBEDS_API_KEY')
            secret = os.getenv('HOTELBEDS_SECRET')
            
            if not api_key or not secret:
                return False
            
            # Test with a simple endpoint
            import hashlib
            import time
            
            timestamp = str(int(time.time()))
            signature = hashlib.sha256((api_key + secret + timestamp).encode()).hexdigest()
            
            headers = {
                'Api-key': api_key,
                'X-Signature': signature,
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.test.hotelbeds.com/hotel-api/1.0/status",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status in [200, 401]  # 401 means API is reachable
                    
        except Exception as e:
            logger.error(f"HotelBeds validation error: {e}")
            return False
    
    async def _validate_geoapify(self) -> bool:
        """Validate Geoapify API key"""
        try:
            api_key = os.getenv('GEOAPIFY_API_KEY')
            if not api_key:
                return False
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://api.geoapify.com/v2/places?categories=commercial&filter=circle:2.3522,48.8566,1000&limit=1&apiKey={api_key}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Geoapify validation error: {e}")
            return False
    
    async def _validate_google_places(self) -> bool:
        """Validate Google Places API key"""
        try:
            api_key = os.getenv('GOOGLE_PLACES_API_KEY')
            if not api_key:
                return False
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://maps.googleapis.com/maps/api/place/details/json?place_id=ChIJN1t_tDeuEmsRUsoyG83frY4&fields=place_id,name&key={api_key}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Google Places validation error: {e}")
            return False
    
    async def _validate_openweather(self) -> bool:
        """Validate OpenWeather API key"""
        try:
            api_key = os.getenv('OPEN_WEATHER_MAP_API_KEY')
            if not api_key:
                return False
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://api.openweathermap.org/data/2.5/weather?q=London&appid={api_key}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"OpenWeather validation error: {e}")
            return False
    
    async def _validate_tomorrow_io(self) -> bool:
        """Validate Tomorrow.io API key"""
        try:
            api_key = os.getenv('TOMORROW_IO_API_KEY')
            if not api_key:
                return False
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://api.tomorrow.io/v4/weather/realtime?location=40.758896,-73.985130&apikey={api_key}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Tomorrow.io validation error: {e}")
            return False
    
    async def _validate_flight_api(self) -> bool:
        """Validate Flight API key"""
        try:
            api_key = os.getenv('FLIGHT_API_KEY')
            if not api_key:
                return False
            
            # Flight API validation would depend on their specific endpoint
            # For now, just check if key exists and has reasonable format
            return len(api_key) > 10
                    
        except Exception as e:
            logger.error(f"Flight API validation error: {e}")
            return False

# Global validator instance
api_validator = APIValidator()