E:\seamless_translator\Lib\site-packages\pydantic\_internal\_config.py:345: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
E:\syntour0903\backend\app\main_enhanced.py:118: DeprecationWarning: `example` has been deprecated, please use `examples` instead
  from routers import amadeus_content, hotelbeds_content, flightapi_content, openweathermap_content, tomorrowio_content, geoapify_content, googleplaces_content, health
2025-09-03 23:25:06,183 - core.env_config - INFO - Using default value for GOOGLE_PLACES_TIMEOUT_SECONDS
2025-09-03 23:25:06,250 - __main__ - INFO - Using API key for Speech-to-Text
E:\syntour0903\backend\app\main_enhanced.py:536: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Will watch for changes in these directories: ['E:\\syntour0903\\backend']
INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
INFO:     Started reloader process [18496] using WatchFiles
E:\seamless_translator\Lib\site-packages\pydantic\_internal\_config.py:345: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
2025-09-03 23:25:08,560 - core.env_config - INFO - Using default value for GOOGLE_PLACES_TIMEOUT_SECONDS
2025-09-03 23:25:08,625 - __mp_main__ - INFO - Using API key for Speech-to-Text
2025-09-03 23:25:08,714 - main_enhanced - INFO - Using API key for Speech-to-Text
INFO:     Started server process [33148]
INFO:     Waiting for application startup.
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 49: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 541, in startup_event
    logger.info("\U0001f680 Starting SynTour API with Phase 1 critical fixes...")
Message: '\U0001f680 Starting SynTour API with Phase 1 critical fixes...'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 82: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 545, in startup_event
    api_results = await api_validator.validate_all_apis()
  File "E:\syntour0903\backend\app\core\api_validator.py", line 42, in validate_all_apis
    logger.info(f"API validation for {api_name}: {'\u2705' if result else '\u274c'}")
Message: 'API validation for amadeus: \u2705'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 84: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 545, in startup_event
    api_results = await api_validator.validate_all_apis()
  File "E:\syntour0903\backend\app\core\api_validator.py", line 42, in validate_all_apis
    logger.info(f"API validation for {api_name}: {'\u2705' if result else '\u274c'}")
Message: 'API validation for hotelbeds: \u2705'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 83: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 545, in startup_event
    api_results = await api_validator.validate_all_apis()
  File "E:\syntour0903\backend\app\core\api_validator.py", line 42, in validate_all_apis
    logger.info(f"API validation for {api_name}: {'\u2705' if result else '\u274c'}")
Message: 'API validation for geoapify: \u2705'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 88: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 545, in startup_event
    api_results = await api_validator.validate_all_apis()
  File "E:\syntour0903\backend\app\core\api_validator.py", line 42, in validate_all_apis
    logger.info(f"API validation for {api_name}: {'\u2705' if result else '\u274c'}")
Message: 'API validation for google_places: \u2705'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 86: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 545, in startup_event
    api_results = await api_validator.validate_all_apis()
  File "E:\syntour0903\backend\app\core\api_validator.py", line 42, in validate_all_apis
    logger.info(f"API validation for {api_name}: {'\u2705' if result else '\u274c'}")
Message: 'API validation for openweather: \u2705'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 86: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 545, in startup_event
    api_results = await api_validator.validate_all_apis()
  File "E:\syntour0903\backend\app\core\api_validator.py", line 42, in validate_all_apis
    logger.info(f"API validation for {api_name}: {'\u2705' if result else '\u274c'}")
Message: 'API validation for tomorrow_io: \u2705'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 85: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 545, in startup_event
    api_results = await api_validator.validate_all_apis()
  File "E:\syntour0903\backend\app\core\api_validator.py", line 42, in validate_all_apis
    logger.info(f"API validation for {api_name}: {'\u2705' if result else '\u274c'}")
Message: 'API validation for flight_api: \u2705'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 49: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 551, in startup_event
    logger.info("\u2705 All APIs validated successfully")
Message: '\u2705 All APIs validated successfully'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 49: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 556, in startup_event
    gemini_model = initialize_genai()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 524, in initialize_genai
    logger.info("\u2705 Vertex AI \u521d\u59cb\u5316\u6210\u529f")
Message: '\u2705 Vertex AI \u521d\u59cb\u5316\u6210\u529f'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 49: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 559, in startup_event
    logger.info("\U0001f680 FastAPI server started successfully with Google Gen AI SDK")
Message: '\U0001f680 FastAPI server started successfully with Google Gen AI SDK'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 49: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 149, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 26, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 75, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 55, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 707, in __call__
    await self.lifespan(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 566, in __aenter__
    await self._router.startup()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 654, in startup
    await handler()
  File "E:\syntour0903\backend\app\main_enhanced.py", line 563, in startup_event
    logger.info("\U0001f3af Phase 1 critical fixes applied successfully")
Message: '\U0001f3af Phase 1 critical fixes applied successfully'
Arguments: ()
INFO:     Application startup complete.
--- Logging error ---
Traceback (most recent call last):
  File "E:\seamless_translator\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\seamless_translator\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 49: character maps to <undefined>
Call stack:
  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=18496, pipe_handle=812)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
  File "E:\seamless_translator\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "E:\seamless_translator\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "E:\seamless_translator\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "E:\seamless_translator\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 66, in app
    response = await func(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 274, in app
    raw_response = await run_endpoint_function(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
  File "E:\syntour0903\backend\app\main_enhanced.py", line 577, in health_check
    genai_status = "connected" if initialize_genai() else "disconnected"
  File "E:\syntour0903\backend\app\main_enhanced.py", line 524, in initialize_genai
    logger.info("\u2705 Vertex AI \u521d\u59cb\u5316\u6210\u529f")
Message: '\u2705 Vertex AI \u521d\u59cb\u5316\u6210\u529f'
Arguments: ()
INFO:     127.0.0.1:52466 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:52596 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:51064 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:51064 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     ('127.0.0.1', 55062) - "WebSocket /ws/speech/client_1756913466827_vwsibabdb" [accepted]
2025-09-03 23:31:07,137 - main_enhanced - INFO - Client client_1756913466827_vwsibabdb connected
INFO:     connection open
2025-09-03 23:31:07,905 - main_enhanced - INFO - WebSocket disconnected for client client_1756913466827_vwsibabdb
2025-09-03 23:31:07,905 - main_enhanced - INFO - Client client_1756913466827_vwsibabdb disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 59460) - "WebSocket /ws/speech/client_1756913468093_1ysjr9q9k" [accepted]
2025-09-03 23:31:08,408 - main_enhanced - INFO - Client client_1756913468093_1ysjr9q9k connected
INFO:     connection open
2025-09-03 23:31:08,601 - main_enhanced - INFO - WebSocket disconnected for client client_1756913468093_1ysjr9q9k
2025-09-03 23:31:08,601 - main_enhanced - INFO - Client client_1756913468093_1ysjr9q9k disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 53509) - "WebSocket /ws/speech/client_1756913468612_vdl6lme0m" [accepted]
2025-09-03 23:31:08,922 - main_enhanced - INFO - Client client_1756913468612_vdl6lme0m connected
INFO:     connection open
INFO:     127.0.0.1:52560 - "OPTIONS /api/ai/chat HTTP/1.1" 400 Bad Request
2025-09-03 23:31:08,932 - main_enhanced - INFO - WebSocket disconnected for client client_1756913468612_vdl6lme0m
2025-09-03 23:31:08,932 - main_enhanced - INFO - Client client_1756913468612_vdl6lme0m disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 60022) - "WebSocket /ws/speech/client_1756913468943_0yple1b8n" [accepted]
2025-09-03 23:31:09,262 - main_enhanced - INFO - Client client_1756913468943_0yple1b8n connected
INFO:     connection open
2025-09-03 23:31:14,251 - main_enhanced - INFO - WebSocket disconnected for client client_1756913468943_0yple1b8n
2025-09-03 23:31:14,251 - main_enhanced - INFO - Client client_1756913468943_0yple1b8n disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 59656) - "WebSocket /ws/speech/client_1756913474251_sgkarixo6" [accepted]
2025-09-03 23:31:14,569 - main_enhanced - INFO - Client client_1756913474251_sgkarixo6 connected
INFO:     connection open
2025-09-03 23:31:19,171 - main_enhanced - INFO - WebSocket disconnected for client client_1756913474251_sgkarixo6
2025-09-03 23:31:19,171 - main_enhanced - INFO - Client client_1756913474251_sgkarixo6 disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 60415) - "WebSocket /ws/speech/client_1756913479582_pbfsjmd8m" [accepted]
2025-09-03 23:31:19,888 - main_enhanced - INFO - Client client_1756913479582_pbfsjmd8m connected
INFO:     connection open
2025-09-03 23:31:26,149 - main_enhanced - INFO - WebSocket disconnected for client client_1756913479582_pbfsjmd8m
2025-09-03 23:31:26,149 - main_enhanced - INFO - Client client_1756913479582_pbfsjmd8m disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 49667) - "WebSocket /ws/speech/client_1756913486302_qnove8hvu" [accepted]
2025-09-03 23:31:26,607 - main_enhanced - INFO - Client client_1756913486302_qnove8hvu connected
INFO:     connection open
2025-09-03 23:31:26,841 - main_enhanced - INFO - WebSocket disconnected for client client_1756913486302_qnove8hvu
2025-09-03 23:31:26,842 - main_enhanced - INFO - Client client_1756913486302_qnove8hvu disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 58582) - "WebSocket /ws/speech/client_1756913486853_ap9ebabxo" [accepted]
2025-09-03 23:31:27,168 - main_enhanced - INFO - Client client_1756913486853_ap9ebabxo connected
INFO:     connection open
INFO:     127.0.0.1:65370 - "OPTIONS /api/ai/chat HTTP/1.1" 400 Bad Request
2025-09-03 23:31:27,174 - main_enhanced - INFO - WebSocket disconnected for client client_1756913486853_ap9ebabxo
2025-09-03 23:31:27,174 - main_enhanced - INFO - Client client_1756913486853_ap9ebabxo disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 59404) - "WebSocket /ws/speech/client_1756913487176_k49sro322" [accepted]
2025-09-03 23:31:27,480 - main_enhanced - INFO - Client client_1756913487176_k49sro322 connected
INFO:     connection open
2025-09-03 23:31:32,487 - main_enhanced - INFO - WebSocket disconnected for client client_1756913487176_k49sro322
2025-09-03 23:31:32,487 - main_enhanced - INFO - Client client_1756913487176_k49sro322 disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 55289) - "WebSocket /ws/speech/client_1756913492487_30j5yzo1h" [accepted]
2025-09-03 23:31:32,796 - main_enhanced - INFO - Client client_1756913492487_30j5yzo1h connected
INFO:     connection open
2025-09-03 23:32:14,636 - main_enhanced - INFO - WebSocket disconnected for client client_1756913492487_30j5yzo1h
2025-09-03 23:32:14,636 - main_enhanced - INFO - Client client_1756913492487_30j5yzo1h disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 53812) - "WebSocket /ws/speech/client_1756913534793_4coyrm2ma" [accepted]
2025-09-03 23:32:15,116 - main_enhanced - INFO - Client client_1756913534793_4coyrm2ma connected
INFO:     connection open
2025-09-03 23:32:15,334 - main_enhanced - INFO - WebSocket disconnected for client client_1756913534793_4coyrm2ma
2025-09-03 23:32:15,334 - main_enhanced - INFO - Client client_1756913534793_4coyrm2ma disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 53105) - "WebSocket /ws/speech/client_1756913535334_u0j58b7ck" [accepted]
2025-09-03 23:32:15,645 - main_enhanced - INFO - Client client_1756913535334_u0j58b7ck connected
INFO:     connection open
INFO:     127.0.0.1:63103 - "OPTIONS /api/ai/chat HTTP/1.1" 400 Bad Request
2025-09-03 23:32:15,670 - main_enhanced - INFO - WebSocket disconnected for client client_1756913535334_u0j58b7ck
2025-09-03 23:32:15,670 - main_enhanced - INFO - Client client_1756913535334_u0j58b7ck disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 50364) - "WebSocket /ws/speech/client_1756913535671_rzmzx4puu" [accepted]
2025-09-03 23:32:15,983 - main_enhanced - INFO - Client client_1756913535671_rzmzx4puu connected
INFO:     connection open
2025-09-03 23:32:18,543 - main_enhanced - INFO - WebSocket disconnected for client client_1756913535671_rzmzx4puu
2025-09-03 23:32:18,543 - main_enhanced - INFO - Client client_1756913535671_rzmzx4puu disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 63284) - "WebSocket /ws/speech/client_1756913539213_rtrh7nkpo" [accepted]
2025-09-03 23:32:19,526 - main_enhanced - INFO - Client client_1756913539213_rtrh7nkpo connected
INFO:     connection open
2025-09-03 23:32:20,950 - main_enhanced - INFO - WebSocket disconnected for client client_1756913539213_rtrh7nkpo
2025-09-03 23:32:20,950 - main_enhanced - INFO - Client client_1756913539213_rtrh7nkpo disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 61074) - "WebSocket /ws/speech/client_1756913541182_m884xagsv" [accepted]
2025-09-03 23:32:21,492 - main_enhanced - INFO - Client client_1756913541182_m884xagsv connected
INFO:     connection open
2025-09-03 23:32:21,770 - main_enhanced - INFO - WebSocket disconnected for client client_1756913541182_m884xagsv
2025-09-03 23:32:21,770 - main_enhanced - INFO - Client client_1756913541182_m884xagsv disconnected
INFO:     connection closed
INFO:     127.0.0.1:55147 - "OPTIONS /api/ai/chat HTTP/1.1" 400 Bad Request
INFO:     ('127.0.0.1', 57371) - "WebSocket /ws/speech/client_1756913541791_zoycs6pzh" [accepted]
2025-09-03 23:32:22,095 - main_enhanced - INFO - Client client_1756913541791_zoycs6pzh connected
INFO:     connection open
2025-09-03 23:32:27,101 - main_enhanced - INFO - WebSocket disconnected for client client_1756913541791_zoycs6pzh
2025-09-03 23:32:27,101 - main_enhanced - INFO - Client client_1756913541791_zoycs6pzh disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 58417) - "WebSocket /ws/speech/client_1756913547101_6q6srm2o5" [accepted]
2025-09-03 23:32:27,409 - main_enhanced - INFO - Client client_1756913547101_6q6srm2o5 connected
INFO:     connection open
2025-09-03 23:33:00,562 - main_enhanced - INFO - WebSocket disconnected for client client_1756913547101_6q6srm2o5
2025-09-03 23:33:00,563 - main_enhanced - INFO - Client client_1756913547101_6q6srm2o5 disconnected
INFO:     connection closed
INFO:     ('127.0.0.1', 59394) - "WebSocket /ws/speech/client_1756914459329_n38d4ovzl" [accepted]
2025-09-03 23:47:39,633 - main_enhanced - INFO - Client client_1756914459329_n38d4ovzl connected
INFO:     connection open
2025-09-03 23:50:50,438 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'E:\\syntour0903\\backend\\server.log')}
2025-09-03 23:50:57,169 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'E:\\syntour0903\\backend\\server.log')}
2025-09-03 23:50:57,534 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'E:\\syntour0903\\backend\\server.log')}
2025-09-03 23:51:00,542 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, 'E:\\syntour0903\\backend\\server.log')}
