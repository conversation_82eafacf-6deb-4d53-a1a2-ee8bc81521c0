# app/services/googleplaces_service.py
import os
import aiohttp
import asyncio
from typing import Optional, List, Dict, Any, Union
from fastapi import HTTPException
import logging

from core.cache_manager import cached_api_call
# BUG-013 Fix: Import environment configuration with defaults
from core.env_config import EnvironmentConfig
# BUG-014 Fix: Import async error handling
from core.async_error_handler import async_api_call, api_error_context, AsyncErrorHandler

from models.googleplaces_model import (
    PlaceDetails,
    PlaceDetailsRequest,
    PlaceDetailsResponse,
    PlacePhotoRequest,
    GooglePlacesError,
    validate_place_id
)

# Configure logging
logger = logging.getLogger(__name__)

# Google Places API configuration with environment defaults
GOOGLE_PLACES_BASE_URL = "https://maps.googleapis.com/maps/api/place"
# BUG-013 Fix: Use configurable timeout with default
GOOGLE_PLACES_TIMEOUT = EnvironmentConfig.get_int_env_var('GOOGLE_PLACES_TIMEOUT_SECONDS', 30)

# BUG-011 Fix: Configurable place fields instead of hardcoded defaults
def get_default_place_fields() -> List[str]:
    """Get configurable default place fields from environment or use fallback"""
    # BUG-011 & BUG-013 Fix: Use environment configuration with defaults
    return EnvironmentConfig.get_list_env_var(
        'GOOGLE_PLACES_DEFAULT_FIELDS',
        default=[
            "place_id", "name", "formatted_address", "geometry", "types",
            "rating", "user_ratings_total", "price_level", "business_status",
            "formatted_phone_number", "website", "opening_hours", "photos"
        ]
    )

def get_summary_place_fields() -> List[str]:
    """Get configurable summary place fields"""
    # BUG-011 & BUG-013 Fix: Use environment configuration with defaults
    return EnvironmentConfig.get_list_env_var(
        'GOOGLE_PLACES_SUMMARY_FIELDS',
        default=[
            "place_id", "name", "formatted_address", "geometry", "types",
            "rating", "user_ratings_total", "business_status", "opening_hours"
        ]
    )

def get_google_places_credentials() -> str:
    """Get Google Places API key from environment variables"""
    # BUG-013 Fix: Use environment configuration for better error handling
    api_key = EnvironmentConfig.get_env_var("GOOGLE_PLACES_API_KEY", required=True)
    return api_key

# BUG-014 Fix: Add comprehensive async error handling
@async_api_call(retry_count=2, timeout=30.0)
async def google_places_request(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    method: str = "GET"
) -> Union[Dict[str, Any], bytes]:
    """Make a request to Google Places API with comprehensive error handling"""
    # BUG-014 Fix: Use async error context for better error handling
    async with api_error_context("Google Places"):
        api_key = get_google_places_credentials()
        
        # Prepare URL and parameters
        url = f"{GOOGLE_PLACES_BASE_URL}{endpoint}"
        request_params = params or {}
        request_params["key"] = api_key
        
        logger.info(f"Google Places API request: {method} {url}")
        logger.debug(f"Request parameters: {request_params}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=GOOGLE_PLACES_TIMEOUT)) as session:
            if method.upper() == "GET":
                async with session.get(url, params=request_params) as response:
                    
                    # Handle photo requests (return binary data)
                    if endpoint.startswith("/photo"):
                        if response.status == 200:
                            logger.info(f"Google Places API photo success: {response.status}")
                            return await response.read()
                        else:
                            response_text = await response.text()
                            logger.error(f"Google Places API error: {response.status} - {response_text}")
                            raise GooglePlacesError(f"Photo API error: {response.status}", response.status)
                    
                    # Handle JSON responses
                    response_text = await response.text()
                    
                    if response.status == 200:
                        try:
                            data = await response.json()
                            logger.info(f"Google Places API success: {response.status}")
                            
                            # Check API-specific status in response
                            api_status = data.get("status", "UNKNOWN_ERROR")
                            if api_status != "OK":
                                logger.error(f"Google Places API status error: {api_status}")
                                raise GooglePlacesError(f"API status error: {api_status}", response.status, api_status)
                            
                            return data
                        except Exception as json_error:
                            logger.error(f"Failed to parse JSON response: {json_error}")
                            raise GooglePlacesError(f"Invalid JSON response: {json_error}")
                    else:
                        logger.error(f"Google Places API error: {response.status} - {response_text}")
                        if response.status == 400:
                            raise GooglePlacesError("Invalid request parameters", response.status)
                        elif response.status == 401:
                            raise GooglePlacesError("Invalid API key", response.status)
                        elif response.status == 403:
                            raise GooglePlacesError("API access forbidden or quota exceeded", response.status)
                        elif response.status == 429:
                            raise GooglePlacesError("API rate limit exceeded", response.status)
                        else:
                            raise GooglePlacesError(f"API error: {response.status}", response.status)
            else:
                raise GooglePlacesError(f"Unsupported HTTP method: {method}")

@cached_api_call("googleplaces", "details", ttl=3600)  # 1 hour cache for place details
async def get_place_details(
    place_id: str,
    fields: Optional[List[str]] = None,
    language: str = "en",
    region: Optional[str] = None,
    session_token: Optional[str] = None
) -> PlaceDetailsResponse:
    """
    Get detailed information about a specific place using the new Places API
    
    Args:
        place_id: A textual identifier that uniquely identifies a place
        fields: List of place data fields to return (affects pricing)
        language: Language code for the response (default: en)
        region: Region code for biasing results
        session_token: Session token for billing optimization
    
    Returns:
        PlaceDetailsResponse containing detailed place information
    
    Raises:
        GooglePlacesError: If the API request fails
        ValueError: If place_id is invalid
    """
    # Validate place_id
    if not validate_place_id(place_id):
        raise ValueError(f"Invalid place_id format: {place_id}")
    
    # Use configurable default fields if none specified to control costs
    if fields is None:
        fields = get_default_place_fields()
    
    # Validate request
    request_data = PlaceDetailsRequest(
        place_id=place_id,
        fields=fields,
        language=language,
        region=region,
        session_token=session_token
    )
    
    params = {
        "place_id": request_data.place_id,
        "fields": ",".join(request_data.fields) if request_data.fields else ",".join(get_default_place_fields()),
        "language": request_data.language
    }
    
    if request_data.region:
        params["region"] = request_data.region
    
    if request_data.session_token:
        params["sessiontoken"] = request_data.session_token
    
    try:
        response_data = await google_places_request("/details/json", params)
        
        # Parse response
        place_details_response = PlaceDetailsResponse(**response_data)
        
        logger.info(f"Successfully retrieved place details for: {place_details_response.result.name}")
        return place_details_response
        
    except Exception as e:
        logger.error(f"Failed to get place details for {place_id}: {str(e)}")
        raise

@cached_api_call("googleplaces", "photo", ttl=86400)  # 24 hours cache for photos
async def get_place_photo(
    photo_reference: str,
    max_width: Optional[int] = None,
    max_height: Optional[int] = None
) -> bytes:
    """
    Get a place photo using the new Places API
    
    Args:
        photo_reference: Photo reference from place details
        max_width: Maximum width of the photo (1-1600)
        max_height: Maximum height of the photo (1-1600)
    
    Returns:
        bytes: Photo data
    
    Raises:
        GooglePlacesError: If the API request fails
        ValueError: If parameters are invalid
    """
    # Validate request
    request_data = PlacePhotoRequest(
        photo_reference=photo_reference,
        max_width=max_width,
        max_height=max_height
    )
    
    params = {
        "photoreference": request_data.photo_reference
    }
    
    # Add dimension constraints (at least one is required)
    if request_data.max_width:
        params["maxwidth"] = request_data.max_width
    elif request_data.max_height:
        params["maxheight"] = request_data.max_height
    else:
        # Default to reasonable width if neither specified
        params["maxwidth"] = 400
    
    if request_data.max_height and request_data.max_width:
        # If both specified, use maxwidth (API preference)
        params.pop("maxheight", None)
    
    try:
        photo_data = await google_places_request("/photo", params)
        
        if not isinstance(photo_data, bytes):
            raise GooglePlacesError("Invalid photo response format")
        
        logger.info(f"Successfully retrieved photo: {len(photo_data)} bytes")
        return photo_data
        
    except Exception as e:
        logger.error(f"Failed to get place photo {photo_reference}: {str(e)}")
        raise

# Utility functions
async def get_place_summary(place_id: str) -> Dict[str, Any]:
    """Get a summary of key place information with minimal fields"""
    # BUG-011 Fix: Use configurable summary fields
    summary_fields = get_summary_place_fields()
    
    try:
        response = await get_place_details(place_id, fields=summary_fields)
        place = response.result
        
        return {
            "place_id": place.place_id,
            "name": place.name,
            "address": place.formatted_address,
            "coordinates": {
                "lat": place.geometry.location["lat"] if place.geometry else None,
                "lng": place.geometry.location["lng"] if place.geometry else None
            },
            "types": place.types,
            "rating": place.rating,
            "total_ratings": place.user_ratings_total,
            "business_status": place.business_status,
            "is_open": place.opening_hours.open_now if place.opening_hours else None
        }
    except Exception as e:
        logger.error(f"Failed to get place summary for {place_id}: {str(e)}")
        raise

async def get_place_photos_info(place_id: str) -> List[Dict[str, Any]]:
    """Get information about available photos for a place"""
    photo_fields = ["place_id", "name", "photos"]
    
    try:
        response = await get_place_details(place_id, fields=photo_fields)
        place = response.result
        
        if not place.photos:
            return []
        
        photos_info = []
        for photo in place.photos:
            photos_info.append({
                "photo_reference": photo.photo_reference,
                "width": photo.width,
                "height": photo.height,
                "html_attributions": photo.html_attributions
            })
        
        logger.info(f"Found {len(photos_info)} photos for place: {place.name}")
        return photos_info
        
    except Exception as e:
        logger.error(f"Failed to get photos info for {place_id}: {str(e)}")
        raise

def calculate_photo_dimensions(
    original_width: int,
    original_height: int,
    max_width: Optional[int] = None,
    max_height: Optional[int] = None
) -> tuple[int, int]:
    """Calculate optimal photo dimensions while maintaining aspect ratio"""
    if not max_width and not max_height:
        return original_width, original_height
    
    aspect_ratio = original_width / original_height
    
    if max_width and max_height:
        # Both constraints - use the more restrictive one
        width_constrained = max_width
        height_constrained = int(max_width / aspect_ratio)
        
        if height_constrained > max_height:
            height_constrained = max_height
            width_constrained = int(max_height * aspect_ratio)
        
        return width_constrained, height_constrained
    
    elif max_width:
        return max_width, int(max_width / aspect_ratio)
    
    else:  # max_height only
        return int(max_height * aspect_ratio), max_height

def validate_photo_reference(photo_reference: str) -> bool:
    """Validate photo reference format"""
    if not photo_reference or not isinstance(photo_reference, str):
        return False
    
    # Google photo references are typically long alphanumeric strings
    return len(photo_reference) >= 10 and photo_reference.replace("-", "").replace("_", "").isalnum()