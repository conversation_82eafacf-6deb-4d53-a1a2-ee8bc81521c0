# app/core/cache_manager.py
import asyncio
import json
import hashlib
import time
from typing import Any, Optional, Dict, Union
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    data: Any
    timestamp: float
    ttl: int
    hits: int = 0

class InMemoryCache:
    """In-memory cache with TTL support"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, CacheEntry] = {}
        self.max_size = max_size
        self.lock = asyncio.Lock()
    
    def _generate_key(self, prefix: str, **kwargs) -> str:
        """Generate cache key from parameters"""
        # Sort kwargs for consistent key generation
        sorted_params = sorted(kwargs.items())
        params_str = json.dumps(sorted_params, sort_keys=True)
        
        # Create hash for long parameter strings
        if len(params_str) > 100:
            params_hash = hashlib.md5(params_str.encode()).hexdigest()
            return f"{prefix}:{params_hash}"
        else:
            return f"{prefix}:{params_str}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        async with self.lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # Check if expired
            if time.time() - entry.timestamp > entry.ttl:
                del self.cache[key]
                return None
            
            # Update hit count
            entry.hits += 1
            return entry.data
    
    async def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """Set value in cache"""
        async with self.lock:
            # Evict old entries if cache is full
            if len(self.cache) >= self.max_size and key not in self.cache:
                await self._evict_lru()
            
            self.cache[key] = CacheEntry(
                data=value,
                timestamp=time.time(),
                ttl=ttl
            )
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        async with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    async def clear(self) -> None:
        """Clear all cache entries"""
        async with self.lock:
            self.cache.clear()
    
    async def _evict_lru(self) -> None:
        """Evict least recently used entry"""
        if not self.cache:
            return
        
        # Find entry with lowest hit count and oldest timestamp
        lru_key = min(
            self.cache.keys(),
            key=lambda k: (self.cache[k].hits, self.cache[k].timestamp)
        )
        del self.cache[lru_key]
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        async with self.lock:
            total_entries = len(self.cache)
            total_hits = sum(entry.hits for entry in self.cache.values())
            
            # Calculate expired entries
            now = time.time()
            expired_count = sum(
                1 for entry in self.cache.values()
                if now - entry.timestamp > entry.ttl
            )
            
            return {
                "total_entries": total_entries,
                "total_hits": total_hits,
                "expired_entries": expired_count,
                "cache_size": total_entries,
                "max_size": self.max_size
            }

class CacheManager:
    """Centralized cache manager for API responses"""
    
    def __init__(self):
        self.cache = InMemoryCache(max_size=2000)
        self.default_ttl = 300  # 5 minutes
        
        # API-specific TTL settings
        self.api_ttls = {
            "amadeus": 1800,      # 30 minutes - flight data changes slowly
            "hotelbeds": 3600,    # 1 hour - hotel data is relatively stable
            "geoapify": 3600,     # 1 hour - location data is stable
            "googleplaces": 3600, # 1 hour - place data is stable
            "openweather": 600,   # 10 minutes - weather changes frequently
            "tomorrow_io": 600,   # 10 minutes - weather changes frequently
            "flightapi": 300      # 5 minutes - flight prices change frequently
        }
    
    def get_cache_key(self, api_name: str, endpoint: str, **params) -> str:
        """Generate standardized cache key"""
        return self.cache._generate_key(f"{api_name}:{endpoint}", **params)
    
    async def get_cached_response(
        self, 
        api_name: str, 
        endpoint: str, 
        **params
    ) -> Optional[Any]:
        """Get cached API response"""
        cache_key = self.get_cache_key(api_name, endpoint, **params)
        
        try:
            result = await self.cache.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for {api_name}:{endpoint}")
                return result
            else:
                logger.debug(f"Cache miss for {api_name}:{endpoint}")
                return None
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            return None
    
    async def cache_response(
        self,
        api_name: str,
        endpoint: str,
        response_data: Any,
        custom_ttl: Optional[int] = None,
        **params
    ) -> None:
        """Cache API response"""
        cache_key = self.get_cache_key(api_name, endpoint, **params)
        ttl = custom_ttl or self.api_ttls.get(api_name, self.default_ttl)
        
        try:
            await self.cache.set(cache_key, response_data, ttl)
            logger.debug(f"Cached response for {api_name}:{endpoint} (TTL: {ttl}s)")
        except Exception as e:
            logger.error(f"Cache set error: {e}")
    
    async def invalidate_cache(self, api_name: str, pattern: Optional[str] = None) -> int:
        """Invalidate cache entries for an API"""
        count = 0
        
        try:
            # Get all keys (this is not efficient for large caches, but works for our use case)
            async with self.cache.lock:
                keys_to_delete = []
                
                for key in self.cache.cache.keys():
                    if key.startswith(f"{api_name}:"):
                        if pattern is None or pattern in key:
                            keys_to_delete.append(key)
                
                for key in keys_to_delete:
                    if key in self.cache.cache:
                        del self.cache.cache[key]
                        count += 1
            
            logger.info(f"Invalidated {count} cache entries for {api_name}")
            return count
            
        except Exception as e:
            logger.error(f"Cache invalidation error: {e}")
            return 0
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        return await self.cache.get_stats()
    
    def should_cache_response(self, api_name: str, status_code: int, response_data: Any) -> bool:
        """Determine if response should be cached"""
        # Don't cache error responses
        if status_code >= 400:
            return False
        
        # Don't cache empty responses
        if not response_data:
            return False
        
        # Don't cache real-time data for certain APIs
        if api_name in ["openweather", "tomorrow_io"] and "current" in str(response_data):
            return False
        
        return True

# Global cache manager instance
cache_manager = CacheManager()

def cached_api_call(api_name: str, endpoint: str, ttl: Optional[int] = None):
    """Decorator for caching API calls"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Try to get from cache first
            cached_result = await cache_manager.get_cached_response(
                api_name, endpoint, **kwargs
            )
            
            if cached_result is not None:
                return cached_result
            
            # Call the actual function
            result = await func(*args, **kwargs)
            
            # Cache the result if it's successful
            # Handle different response types
            should_cache = False
            if result:
                if hasattr(result, 'success') and result.success:
                    should_cache = True
                elif isinstance(result, dict) and result.get('success', True):
                    should_cache = True
                elif isinstance(result, (dict, list, str, bytes)) and result:
                    should_cache = True
            
            if should_cache:
                await cache_manager.cache_response(
                    api_name, endpoint, result, ttl, **kwargs
                )
            
            return result
        
        return wrapper
    return decorator