# app/routers/flightapi_content.py
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Optional, List
from datetime import datetime, date, timedelta

from services.flightapi_service import (
    search_oneway_flights,
    search_roundtrip_flights,
    get_airport_schedule,
    get_airport_departures,
    get_airport_arrivals,
    validate_date_format,
    validate_time_format,
    validate_iata_code,
    validate_airline_code,
    calculate_flight_statistics,
    filter_flights_by_criteria,
    sort_flights
)
from models.flightapi_model import (
    OnewayTripResponse,
    RoundTripResponse,
    AirportScheduleResponse,
    OnewayTripRequest,
    RoundTripRequest,
    AirportScheduleRequest,
    CabinClass,
    SortBy,
    FlightSearchResponse,
    FlightAPIErrorResponse
)

router = APIRouter(prefix="/flightapi", tags=["FlightAPI"])

@router.get("/oneway", response_model=OnewayTripResponse)
async def search_oneway_trip(
    origin: str = Query(..., description="Origin airport IATA code (e.g., BOS)"),
    destination: str = Query(..., description="Destination airport IATA code (e.g., CHI)"),
    departure_date: str = Query(..., description="Departure date in YYYY-MM-DD format"),
    adults: int = Query(1, ge=1, le=9, description="Number of adult passengers (1-9)"),
    children: Optional[int] = Query(None, ge=0, le=9, description="Number of child passengers (0-9)"),
    infants: Optional[int] = Query(None, ge=0, le=9, description="Number of infant passengers (0-9)"),
    cabin_class: Optional[CabinClass] = Query(None, description="Preferred cabin class"),
    currency: str = Query("USD", description="Currency code for prices (e.g., USD, EUR)"),
    max_results: int = Query(50, ge=1, le=100, description="Maximum number of results (1-100)"),
    sort_by: SortBy = Query(SortBy.PRICE, description="Sort results by criteria"),
    max_stops: Optional[int] = Query(None, ge=0, le=3, description="Maximum number of stops (0-3)"),
    airlines: Optional[str] = Query(None, description="Preferred airline codes (comma-separated, e.g., AA,DL)"),
    exclude_airlines: Optional[str] = Query(None, description="Airlines to exclude (comma-separated, e.g., UA,WN)"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum price filter"),
    departure_time_from: Optional[str] = Query(None, description="Earliest departure time (HH:MM format)"),
    departure_time_to: Optional[str] = Query(None, description="Latest departure time (HH:MM format)")
):
    """
    Search for one-way flight offers.
    
    This endpoint searches for the best one-way flight options between two cities.
    Results include flight details, pricing, airline information, and schedules.
    """
    # Validate input parameters
    if not validate_iata_code(origin):
        raise HTTPException(status_code=400, detail="Invalid origin IATA code format")
    
    if not validate_iata_code(destination):
        raise HTTPException(status_code=400, detail="Invalid destination IATA code format")
    
    if origin == destination:
        raise HTTPException(status_code=400, detail="Origin and destination cannot be the same")
    
    if not validate_date_format(departure_date):
        raise HTTPException(status_code=400, detail="Invalid departure date format. Use YYYY-MM-DD")
    
    # Check if departure date is not in the past
    departure_dt = datetime.strptime(departure_date, "%Y-%m-%d").date()
    if departure_dt < date.today():
        raise HTTPException(status_code=400, detail="Departure date cannot be in the past")
    
    # Validate time formats if provided
    if departure_time_from and not validate_time_format(departure_time_from):
        raise HTTPException(status_code=400, detail="Invalid departure_time_from format. Use HH:MM")
    
    if departure_time_to and not validate_time_format(departure_time_to):
        raise HTTPException(status_code=400, detail="Invalid departure_time_to format. Use HH:MM")
    
    # Parse airline codes
    airline_codes = None
    exclude_airline_codes = None
    
    if airlines:
        airline_codes = [code.strip().upper() for code in airlines.split(",")]
        for code in airline_codes:
            if not validate_airline_code(code):
                raise HTTPException(status_code=400, detail=f"Invalid airline code: {code}")
    
    if exclude_airlines:
        exclude_airline_codes = [code.strip().upper() for code in exclude_airlines.split(",")]
        for code in exclude_airline_codes:
            if not validate_airline_code(code):
                raise HTTPException(status_code=400, detail=f"Invalid exclude airline code: {code}")
    
    return await search_oneway_flights(
        origin=origin,
        destination=destination,
        departure_date=departure_date,
        adults=adults,
        children=children,
        infants=infants,
        cabin_class=cabin_class.value if cabin_class else None,
        currency=currency,
        max_results=max_results,
        sort_by=sort_by.value,
        max_stops=max_stops,
        airlines=airline_codes,
        exclude_airlines=exclude_airline_codes,
        max_price=max_price,
        departure_time_from=departure_time_from,
        departure_time_to=departure_time_to
    )

@router.post("/oneway", response_model=OnewayTripResponse)
async def search_oneway_trip_post(
    request: OnewayTripRequest = Body(..., description="One-way trip search request")
):
    """
    Search for one-way flights using POST method with detailed parameters.
    
    This endpoint provides the same functionality as the GET method but allows
    for more complex request structures and better parameter organization.
    """
    # Validate the request
    if not validate_iata_code(request.origin):
        raise HTTPException(status_code=400, detail="Invalid origin IATA code format")
    
    if not validate_iata_code(request.destination):
        raise HTTPException(status_code=400, detail="Invalid destination IATA code format")
    
    if request.origin == request.destination:
        raise HTTPException(status_code=400, detail="Origin and destination cannot be the same")
    
    if not validate_date_format(request.departure_date):
        raise HTTPException(status_code=400, detail="Invalid departure date format. Use YYYY-MM-DD")
    
    # Check if departure date is not in the past
    departure_dt = datetime.strptime(request.departure_date, "%Y-%m-%d").date()
    if departure_dt < date.today():
        raise HTTPException(status_code=400, detail="Departure date cannot be in the past")
    
    return await search_oneway_flights(
        origin=request.origin,
        destination=request.destination,
        departure_date=request.departure_date,
        adults=request.adults,
        children=request.children,
        infants=request.infants,
        cabin_class=request.cabin_class.value if request.cabin_class else None,
        currency=request.currency,
        max_results=request.max_results,
        sort_by=request.sort_by.value,
        max_stops=request.max_stops,
        airlines=request.airlines,
        exclude_airlines=request.exclude_airlines,
        max_price=request.max_price,
        departure_time_from=request.departure_time_from,
        departure_time_to=request.departure_time_to
    )

@router.get("/roundtrip", response_model=RoundTripResponse)
async def search_roundtrip_flights(
    origin: str = Query(..., description="Origin airport IATA code (e.g., BOS)"),
    destination: str = Query(..., description="Destination airport IATA code (e.g., CHI)"),
    departure_date: str = Query(..., description="Departure date in YYYY-MM-DD format"),
    return_date: str = Query(..., description="Return date in YYYY-MM-DD format"),
    adults: int = Query(1, ge=1, le=9, description="Number of adult passengers (1-9)"),
    children: Optional[int] = Query(None, ge=0, le=9, description="Number of child passengers (0-9)"),
    infants: Optional[int] = Query(None, ge=0, le=9, description="Number of infant passengers (0-9)"),
    cabin_class: Optional[CabinClass] = Query(None, description="Preferred cabin class"),
    currency: str = Query("USD", description="Currency code for prices (e.g., USD, EUR)"),
    max_results: int = Query(50, ge=1, le=100, description="Maximum number of results (1-100)"),
    sort_by: SortBy = Query(SortBy.PRICE, description="Sort results by criteria"),
    max_stops: Optional[int] = Query(None, ge=0, le=3, description="Maximum number of stops (0-3)"),
    airlines: Optional[str] = Query(None, description="Preferred airline codes (comma-separated)"),
    exclude_airlines: Optional[str] = Query(None, description="Airlines to exclude (comma-separated)"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum price filter"),
    departure_time_from: Optional[str] = Query(None, description="Earliest departure time (HH:MM)"),
    departure_time_to: Optional[str] = Query(None, description="Latest departure time (HH:MM)"),
    return_time_from: Optional[str] = Query(None, description="Earliest return time (HH:MM)"),
    return_time_to: Optional[str] = Query(None, description="Latest return time (HH:MM)")
):
    """
    Search for round-trip flight offers.
    
    This endpoint searches for the best round-trip flight options between two cities.
    Results include outbound and return flight details, total pricing, and schedules.
    """
    # Validate input parameters
    if not validate_iata_code(origin):
        raise HTTPException(status_code=400, detail="Invalid origin IATA code format")
    
    if not validate_iata_code(destination):
        raise HTTPException(status_code=400, detail="Invalid destination IATA code format")
    
    if origin == destination:
        raise HTTPException(status_code=400, detail="Origin and destination cannot be the same")
    
    if not validate_date_format(departure_date):
        raise HTTPException(status_code=400, detail="Invalid departure date format. Use YYYY-MM-DD")
    
    if not validate_date_format(return_date):
        raise HTTPException(status_code=400, detail="Invalid return date format. Use YYYY-MM-DD")
    
    # Check date logic
    departure_dt = datetime.strptime(departure_date, "%Y-%m-%d").date()
    return_dt = datetime.strptime(return_date, "%Y-%m-%d").date()
    
    if departure_dt < date.today():
        raise HTTPException(status_code=400, detail="Departure date cannot be in the past")
    
    if return_dt <= departure_dt:
        raise HTTPException(status_code=400, detail="Return date must be after departure date")
    
    # Validate time formats if provided
    time_fields = [
        (departure_time_from, "departure_time_from"),
        (departure_time_to, "departure_time_to"),
        (return_time_from, "return_time_from"),
        (return_time_to, "return_time_to")
    ]
    
    for time_value, field_name in time_fields:
        if time_value and not validate_time_format(time_value):
            raise HTTPException(status_code=400, detail=f"Invalid {field_name} format. Use HH:MM")
    
    # Parse airline codes
    airline_codes = None
    exclude_airline_codes = None
    
    if airlines:
        airline_codes = [code.strip().upper() for code in airlines.split(",")]
        for code in airline_codes:
            if not validate_airline_code(code):
                raise HTTPException(status_code=400, detail=f"Invalid airline code: {code}")
    
    if exclude_airlines:
        exclude_airline_codes = [code.strip().upper() for code in exclude_airlines.split(",")]
        for code in exclude_airline_codes:
            if not validate_airline_code(code):
                raise HTTPException(status_code=400, detail=f"Invalid exclude airline code: {code}")
    
    return await search_roundtrip_flights(
        origin=origin,
        destination=destination,
        departure_date=departure_date,
        return_date=return_date,
        adults=adults,
        children=children,
        infants=infants,
        cabin_class=cabin_class.value if cabin_class else None,
        currency=currency,
        max_results=max_results,
        sort_by=sort_by.value,
        max_stops=max_stops,
        airlines=airline_codes,
        exclude_airlines=exclude_airline_codes,
        max_price=max_price,
        departure_time_from=departure_time_from,
        departure_time_to=departure_time_to,
        return_time_from=return_time_from,
        return_time_to=return_time_to
    )

@router.post("/roundtrip", response_model=RoundTripResponse)
async def search_roundtrip_flights_post(
    request: RoundTripRequest = Body(..., description="Round-trip search request")
):
    """
    Search for round-trip flights using POST method with detailed parameters.
    """
    # Validate the request
    if not validate_iata_code(request.origin):
        raise HTTPException(status_code=400, detail="Invalid origin IATA code format")
    
    if not validate_iata_code(request.destination):
        raise HTTPException(status_code=400, detail="Invalid destination IATA code format")
    
    if request.origin == request.destination:
        raise HTTPException(status_code=400, detail="Origin and destination cannot be the same")
    
    if not validate_date_format(request.departure_date):
        raise HTTPException(status_code=400, detail="Invalid departure date format. Use YYYY-MM-DD")
    
    if not validate_date_format(request.return_date):
        raise HTTPException(status_code=400, detail="Invalid return date format. Use YYYY-MM-DD")
    
    # Check date logic
    departure_dt = datetime.strptime(request.departure_date, "%Y-%m-%d").date()
    return_dt = datetime.strptime(request.return_date, "%Y-%m-%d").date()
    
    if departure_dt < date.today():
        raise HTTPException(status_code=400, detail="Departure date cannot be in the past")
    
    if return_dt <= departure_dt:
        raise HTTPException(status_code=400, detail="Return date must be after departure date")
    
    return await search_roundtrip_flights(
        origin=request.origin,
        destination=request.destination,
        departure_date=request.departure_date,
        return_date=request.return_date,
        adults=request.adults,
        children=request.children,
        infants=request.infants,
        cabin_class=request.cabin_class.value if request.cabin_class else None,
        currency=request.currency,
        max_results=request.max_results,
        sort_by=request.sort_by.value,
        max_stops=request.max_stops,
        airlines=request.airlines,
        exclude_airlines=request.exclude_airlines,
        max_price=request.max_price,
        departure_time_from=request.departure_time_from,
        departure_time_to=request.departure_time_to,
        return_time_from=request.return_time_from,
        return_time_to=request.return_time_to
    )

@router.get("/schedule", response_model=AirportScheduleResponse)
async def get_airport_schedule_info(
    airport: str = Query(..., description="Airport IATA code (e.g., BOS)"),
    date: str = Query(..., description="Date in YYYY-MM-DD format"),
    type: str = Query("both", description="Schedule type: 'departures', 'arrivals', or 'both'"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of flights (1-500)"),
    airline: Optional[str] = Query(None, description="Filter by airline IATA code"),
    flight_number: Optional[str] = Query(None, description="Filter by specific flight number")
):
    """
    Get airport schedule information for departures and/or arrivals.
    
    This endpoint provides real-time and scheduled flight information for a specific
    airport on a given date. Useful for tracking airport activity and flight schedules.
    """
    # Validate input parameters
    if not validate_iata_code(airport):
        raise HTTPException(status_code=400, detail="Invalid airport IATA code format")
    
    if not validate_date_format(date):
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    
    if type not in ["departures", "arrivals", "both"]:
        raise HTTPException(status_code=400, detail="Type must be 'departures', 'arrivals', or 'both'")
    
    # Validate airline code if provided
    if airline and not validate_airline_code(airline):
        raise HTTPException(status_code=400, detail="Invalid airline code format")
    
    # Validate flight number if provided
    if flight_number and not flight_number.isdigit():
        raise HTTPException(status_code=400, detail="Flight number must be numeric")
    
    return await get_airport_schedule(
        airport=airport,
        date=date,
        schedule_type=type,
        limit=limit,
        airline=airline,
        flight_number=flight_number
    )

@router.get("/schedule/departures", response_model=AirportScheduleResponse)
async def get_departures(
    airport: str = Query(..., description="Airport IATA code"),
    date: str = Query(..., description="Date in YYYY-MM-DD format"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of flights"),
    airline: Optional[str] = Query(None, description="Filter by airline IATA code")
):
    """
    Get airport departure schedule for a specific date.
    
    Returns all scheduled and real-time departure information for the specified airport.
    """
    if not validate_iata_code(airport):
        raise HTTPException(status_code=400, detail="Invalid airport IATA code format")
    
    if not validate_date_format(date):
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    
    if airline and not validate_airline_code(airline):
        raise HTTPException(status_code=400, detail="Invalid airline code format")
    
    return await get_airport_departures(
        airport=airport,
        date=date,
        limit=limit,
        airline=airline
    )

@router.get("/schedule/arrivals", response_model=AirportScheduleResponse)
async def get_arrivals(
    airport: str = Query(..., description="Airport IATA code"),
    date: str = Query(..., description="Date in YYYY-MM-DD format"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of flights"),
    airline: Optional[str] = Query(None, description="Filter by airline IATA code")
):
    """
    Get airport arrival schedule for a specific date.
    
    Returns all scheduled and real-time arrival information for the specified airport.
    """
    if not validate_iata_code(airport):
        raise HTTPException(status_code=400, detail="Invalid airport IATA code format")
    
    if not validate_date_format(date):
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    
    if airline and not validate_airline_code(airline):
        raise HTTPException(status_code=400, detail="Invalid airline code format")
    
    return await get_airport_arrivals(
        airport=airport,
        date=date,
        limit=limit,
        airline=airline
    )

@router.post("/schedule", response_model=AirportScheduleResponse)
async def get_airport_schedule_post(
    request: AirportScheduleRequest = Body(..., description="Airport schedule request")
):
    """
    Get airport schedule using POST method with detailed parameters.
    """
    if not validate_iata_code(request.airport):
        raise HTTPException(status_code=400, detail="Invalid airport IATA code format")
    
    if not validate_date_format(request.date):
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    
    if request.type not in ["departures", "arrivals", "both"]:
        raise HTTPException(status_code=400, detail="Type must be 'departures', 'arrivals', or 'both'")
    
    if request.airline and not validate_airline_code(request.airline):
        raise HTTPException(status_code=400, detail="Invalid airline code format")
    
    if request.flight_number and not request.flight_number.isdigit():
        raise HTTPException(status_code=400, detail="Flight number must be numeric")
    
    return await get_airport_schedule(
        airport=request.airport,
        date=request.date,
        schedule_type=request.type,
        limit=request.limit,
        airline=request.airline,
        flight_number=request.flight_number
    )

@router.get("/health")
async def health_check():
    """Health check endpoint for FlightAPI integration"""
    return {
        "status": "healthy",
        "service": "flightapi",
        "endpoints": [
            "oneway",
            "roundtrip",
            "schedule",
            "schedule/departures",
            "schedule/arrivals"
        ],
        "version": "1.0.0"
    }