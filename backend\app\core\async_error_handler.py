# app/core/async_error_handler.py
import asyncio
import functools
import logging
from typing import Any, Callable, Optional, Type, Union, Dict
from contextlib import asynccontextmanager
import traceback
from datetime import datetime

logger = logging.getLogger(__name__)

class AsyncErrorHandler:
    """Centralized async error handling with retry and fallback mechanisms"""
    
    @staticmethod
    def handle_async_errors(
        retry_count: int = 0,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        catch_exceptions: tuple = (Exception,),
        fallback_value: Any = None,
        log_errors: bool = True
    ):
        """
        Decorator for comprehensive async error handling with retry logic
        
        Args:
            retry_count: Number of retry attempts
            retry_delay: Initial delay between retries (seconds)
            retry_backoff: Backoff multiplier for retry delay
            catch_exceptions: Tuple of exception types to catch
            fallback_value: Value to return if all retries fail
            log_errors: Whether to log errors
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                last_exception = None
                current_delay = retry_delay
                
                for attempt in range(retry_count + 1):
                    try:
                        return await func(*args, **kwargs)
                    
                    except catch_exceptions as e:
                        last_exception = e
                        
                        if log_errors:
                            if attempt < retry_count:
                                logger.warning(
                                    f"Attempt {attempt + 1}/{retry_count + 1} failed for {func.__name__}: {e}. "
                                    f"Retrying in {current_delay}s..."
                                )
                            else:
                                logger.error(
                                    f"All {retry_count + 1} attempts failed for {func.__name__}: {e}",
                                    exc_info=True
                                )
                        
                        # Don't sleep after the last attempt
                        if attempt < retry_count:
                            await asyncio.sleep(current_delay)
                            current_delay *= retry_backoff
                    
                    except Exception as e:
                        # Unexpected exception - don't retry
                        if log_errors:
                            logger.error(
                                f"Unexpected error in {func.__name__}: {e}",
                                exc_info=True
                            )
                        raise
                
                # All retries failed
                if fallback_value is not None:
                    if log_errors:
                        logger.info(f"Returning fallback value for {func.__name__}")
                    return fallback_value
                
                # Re-raise the last exception
                raise last_exception
            
            return wrapper
        return decorator
    
    @staticmethod
    @asynccontextmanager
    async def error_context(
        operation_name: str,
        reraise: bool = True,
        log_errors: bool = True,
        fallback_value: Any = None
    ):
        """
        Async context manager for error handling
        
        Args:
            operation_name: Name of the operation for logging
            reraise: Whether to reraise exceptions
            log_errors: Whether to log errors
            fallback_value: Value to yield if error occurs and reraise=False
        """
        try:
            yield
        except Exception as e:
            if log_errors:
                logger.error(
                    f"Error in {operation_name}: {e}",
                    exc_info=True,
                    extra={
                        'operation': operation_name,
                        'error_type': type(e).__name__,
                        'timestamp': datetime.utcnow().isoformat()
                    }
                )
            
            if reraise:
                raise
            else:
                yield fallback_value
    
    @staticmethod
    async def safe_gather(*awaitables, return_exceptions: bool = True, log_errors: bool = True):
        """
        Safe version of asyncio.gather with comprehensive error handling
        
        Args:
            *awaitables: Async functions to execute concurrently
            return_exceptions: Whether to return exceptions instead of raising
            log_errors: Whether to log errors
            
        Returns:
            List of results or exceptions
        """
        try:
            results = await asyncio.gather(*awaitables, return_exceptions=return_exceptions)
            
            if log_errors and return_exceptions:
                # Log any exceptions in the results
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(
                            f"Task {i} failed in safe_gather: {result}",
                            exc_info=result
                        )
            
            return results
            
        except Exception as e:
            if log_errors:
                logger.error(f"Error in safe_gather: {e}", exc_info=True)
            
            if return_exceptions:
                return [e] * len(awaitables)
            else:
                raise
    
    @staticmethod
    async def timeout_wrapper(
        coro,
        timeout_seconds: float,
        timeout_message: str = "Operation timed out",
        fallback_value: Any = None
    ):
        """
        Wrapper for async operations with timeout handling
        
        Args:
            coro: Coroutine to execute
            timeout_seconds: Timeout in seconds
            timeout_message: Message for timeout exception
            fallback_value: Value to return on timeout (if not None, won't raise)
            
        Returns:
            Result of coroutine or fallback_value on timeout
        """
        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except asyncio.TimeoutError:
            logger.warning(f"Timeout after {timeout_seconds}s: {timeout_message}")
            
            if fallback_value is not None:
                return fallback_value
            else:
                raise asyncio.TimeoutError(timeout_message)
    
    @staticmethod
    def create_task_with_error_handling(
        coro,
        task_name: str = "unnamed_task",
        log_errors: bool = True
    ) -> asyncio.Task:
        """
        Create an asyncio task with automatic error handling
        
        Args:
            coro: Coroutine to run as task
            task_name: Name for logging purposes
            log_errors: Whether to log errors
            
        Returns:
            asyncio.Task with error handling
        """
        async def wrapped_coro():
            try:
                return await coro
            except Exception as e:
                if log_errors:
                    logger.error(
                        f"Error in task '{task_name}': {e}",
                        exc_info=True,
                        extra={'task_name': task_name}
                    )
                raise
        
        task = asyncio.create_task(wrapped_coro())
        task.set_name(task_name)
        return task

# Convenience decorators for common use cases
def async_api_call(retry_count: int = 2, timeout: float = 30.0):
    """Decorator for API calls with retry and timeout"""
    def decorator(func):
        @AsyncErrorHandler.handle_async_errors(
            retry_count=retry_count,
            retry_delay=1.0,
            retry_backoff=2.0,
            catch_exceptions=(asyncio.TimeoutError, ConnectionError, OSError)
        )
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await AsyncErrorHandler.timeout_wrapper(
                func(*args, **kwargs),
                timeout,
                f"API call {func.__name__} timed out"
            )
        return wrapper
    return decorator

def async_database_operation(retry_count: int = 3):
    """Decorator for database operations with retry"""
    return AsyncErrorHandler.handle_async_errors(
        retry_count=retry_count,
        retry_delay=0.5,
        retry_backoff=1.5,
        catch_exceptions=(ConnectionError, OSError, TimeoutError)
    )

def async_file_operation(fallback_value=None):
    """Decorator for file operations with fallback"""
    return AsyncErrorHandler.handle_async_errors(
        retry_count=1,
        retry_delay=0.1,
        catch_exceptions=(IOError, OSError, PermissionError),
        fallback_value=fallback_value
    )

# Context managers for specific use cases
@asynccontextmanager
async def api_error_context(api_name: str):
    """Context manager for API operations"""
    async with AsyncErrorHandler.error_context(
        f"{api_name} API operation",
        reraise=True,
        log_errors=True
    ):
        yield

@asynccontextmanager
async def safe_operation_context(operation_name: str, fallback_value=None):
    """Context manager for operations that should not fail the entire process"""
    async with AsyncErrorHandler.error_context(
        operation_name,
        reraise=False,
        log_errors=True,
        fallback_value=fallback_value
    ):
        yield

# Utility functions for error analysis
def get_error_summary(exception: Exception) -> Dict[str, Any]:
    """Get a comprehensive summary of an exception"""
    return {
        'type': type(exception).__name__,
        'message': str(exception),
        'traceback': traceback.format_exc(),
        'timestamp': datetime.utcnow().isoformat(),
        'module': getattr(exception, '__module__', 'unknown'),
    }

async def log_async_performance(func_name: str, start_time: float, end_time: float, success: bool = True):
    """Log performance metrics for async operations"""
    duration = end_time - start_time
    status = "SUCCESS" if success else "FAILED"
    
    logger.info(
        f"ASYNC_PERF: {func_name} {status} in {duration:.3f}s",
        extra={
            'function': func_name,
            'duration': duration,
            'success': success,
            'timestamp': datetime.utcnow().isoformat()
        }
    )