#!/usr/bin/env python3
"""
Amadeus Flight API Test Script

This script tests the Amadeus Flight API integration using the updated
service layer and models. It demonstrates various API endpoints and validates
the response structure against our Pydantic models.

Documentation: https://developers.amadeus.com/self-service/apis-docs/
"""

import asyncio
import json
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime, date, timedelta

# Load environment variables FIRST before importing any modules
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add the backend app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Now import the service modules after environment variables are loaded
from app.services.amadeus_service import (
    search_flight_offers,
    get_flight_status,
    search_cheapest_flight_dates,
    search_airports,
    get_airline_routes,
    token_manager
)
from app.models.amadeus_model import (
    FlightOffersResponse,
    FlightStatusResponse,
    FlightDatesResponse,
    TravelClass
)

class AmadeusAPITester:
    """Test class for Amadeus Flight API endpoints"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
    
    def log_test(self, test_name: str, success: bool, details: Optional[Dict[str, Any]] = None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results.append(result)
        
        if not success:
            self.failed_tests.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details and not success:
            print(f"   Error: {details}")
    
    async def test_authentication(self):
        """Test Amadeus API authentication"""
        try:
            print("\n🔐 Testing Authentication...")
            
            # Test token acquisition
            token = await token_manager.get_access_token()
            
            self.log_test(
                "Authentication - Token Acquisition",
                token is not None and len(token) > 0,
                {"token_length": len(token) if token else 0}
            )
            
        except Exception as e:
            self.log_test("Authentication", False, {"error": str(e)})
    
    async def test_flight_offers_search(self):
        """Test flight offers search endpoint"""
        try:
            print("\n✈️ Testing Flight Offers Search...")
            
            # Test basic flight search (BOS to CHI)
            tomorrow = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
            next_week = (date.today() + timedelta(days=7)).strftime("%Y-%m-%d")
            
            response = await search_flight_offers(
                origin_location_code="BOS",
                destination_location_code="CHI",
                departure_date=tomorrow,
                adults=1,
                max_results=5
            )
            
            # Validate response structure
            if "data" in response:
                offers_count = len(response["data"])
                self.log_test(
                    "Flight Offers - Basic Search",
                    True,
                    {"offers_count": offers_count, "route": "BOS-CHI"}
                )
                
                # Test first offer structure
                if offers_count > 0:
                    first_offer = response["data"][0]
                    required_fields = ["id", "itineraries", "price"]
                    missing_fields = [field for field in required_fields if field not in first_offer]
                    
                    self.log_test(
                        "Flight Offer Structure",
                        len(missing_fields) == 0,
                        {
                            "missing_fields": missing_fields,
                            "available_fields": list(first_offer.keys())[:10],
                            "price": first_offer.get("price", {}).get("total", "N/A")
                        }
                    )
                else:
                    self.log_test("Flight Offer Structure", False, {"error": "No offers returned"})
            else:
                self.log_test("Flight Offers - Basic Search", False, {"error": "No data key in response"})
            
            # Test round-trip search
            round_trip_response = await search_flight_offers(
                origin_location_code="NYC",
                destination_location_code="LAX",
                departure_date=tomorrow,
                return_date=next_week,
                adults=1,
                max_results=3
            )
            
            self.log_test(
                "Flight Offers - Round Trip",
                "data" in round_trip_response,
                {"route": "NYC-LAX", "round_trip": True}
            )
            
        except Exception as e:
            self.log_test("Flight Offers Search", False, {"error": str(e)})
    
    async def test_flight_status(self):
        """Test flight status endpoint"""
        try:
            print("\n📊 Testing Flight Status...")
            
            # Test with a common flight (American Airlines)
            tomorrow = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
            
            try:
                response = await get_flight_status(
                    carrier_code="AA",
                    flight_number="100",
                    scheduled_departure_date=tomorrow
                )
                
                if "data" in response:
                    flights_count = len(response["data"])
                    self.log_test(
                        "Flight Status - Valid Flight",
                        True,
                        {"flights_count": flights_count, "flight": "AA100"}
                    )
                    
                    # Test flight data structure
                    if flights_count > 0:
                        first_flight = response["data"][0]
                        required_fields = ["flightDesignator", "flightPoints"]
                        has_required = all(field in first_flight for field in required_fields)
                        
                        self.log_test(
                            "Flight Status Structure",
                            has_required,
                            {
                                "available_fields": list(first_flight.keys()),
                                "flight_designator": first_flight.get("flightDesignator", {})
                            }
                        )
                else:
                    self.log_test("Flight Status - Valid Flight", True, {"response": "No data (expected for test flight)"})
                    
            except Exception as e:
                # Flight status might not be available for test flights
                self.log_test("Flight Status - Valid Flight", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Flight Status API", False, {"error": str(e)})
    
    async def test_cheapest_dates(self):
        """Test cheapest flight dates endpoint"""
        try:
            print("\n💰 Testing Cheapest Flight Dates...")
            
            # Test cheapest dates search
            response = await search_cheapest_flight_dates(
                origin="BOS",
                destination="CHI"
            )
            
            if "data" in response:
                dates_count = len(response["data"])
                self.log_test(
                    "Cheapest Dates - Basic Search",
                    True,
                    {"dates_count": dates_count, "route": "BOS-CHI"}
                )
                
                # Test date entry structure
                if dates_count > 0:
                    first_date = response["data"][0]
                    required_fields = ["origin", "destination", "price"]
                    missing_fields = [field for field in required_fields if field not in first_date]
                    
                    self.log_test(
                        "Cheapest Dates Structure",
                        len(missing_fields) == 0,
                        {
                            "missing_fields": missing_fields,
                            "sample_price": first_date.get("price", {}).get("total", "N/A")
                        }
                    )
                else:
                    self.log_test("Cheapest Dates Structure", False, {"error": "No dates returned"})
            else:
                self.log_test("Cheapest Dates - Basic Search", False, {"error": "No data key in response"})
            
            # Test with additional parameters
            filtered_response = await search_cheapest_flight_dates(
                origin="NYC",
                destination="LAX",
                one_way=True,
                non_stop=True
            )
            
            self.log_test(
                "Cheapest Dates - Filtered Search",
                "data" in filtered_response,
                {"route": "NYC-LAX", "filters": "one_way, non_stop"}
            )
            
        except Exception as e:
            self.log_test("Cheapest Dates API", False, {"error": str(e)})
    
    async def test_airport_search(self):
        """Test airport search endpoint"""
        try:
            print("\n🏢 Testing Airport Search...")
            
            # Test airport search by city
            response = await search_airports(keyword="Boston")
            
            if "data" in response:
                airports_count = len(response["data"])
                self.log_test(
                    "Airport Search - By City",
                    True,
                    {"airports_count": airports_count, "keyword": "Boston"}
                )
                
                # Test airport data structure
                if airports_count > 0:
                    first_airport = response["data"][0]
                    required_fields = ["iataCode"]
                    has_required = all(field in first_airport for field in required_fields)
                    
                    self.log_test(
                        "Airport Data Structure",
                        has_required,
                        {
                            "sample_airport": first_airport.get("iataCode", "N/A"),
                            "available_fields": list(first_airport.keys())
                        }
                    )
                else:
                    self.log_test("Airport Data Structure", False, {"error": "No airports returned"})
            else:
                self.log_test("Airport Search - By City", False, {"error": "No data key in response"})
            
            # Test search by IATA code
            iata_response = await search_airports(keyword="BOS")
            self.log_test(
                "Airport Search - By IATA Code",
                "data" in iata_response,
                {"keyword": "BOS"}
            )
            
        except Exception as e:
            self.log_test("Airport Search API", False, {"error": str(e)})
    
    async def test_error_handling(self):
        """Test error handling scenarios"""
        try:
            print("\n⚠️ Testing Error Handling...")
            
            # Test invalid airport code
            try:
                await search_flight_offers(
                    origin_location_code="INVALID",
                    destination_location_code="CHI",
                    departure_date=(date.today() + timedelta(days=1)).strftime("%Y-%m-%d"),
                    adults=1
                )
                self.log_test("Error Handling - Invalid Airport", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Invalid Airport", True, {"expected_error": str(e)})
            
            # Test past date
            try:
                yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
                await search_flight_offers(
                    origin_location_code="BOS",
                    destination_location_code="CHI",
                    departure_date=yesterday,
                    adults=1
                )
                self.log_test("Error Handling - Past Date", False, {"error": "Should have raised an exception"})
            except Exception as e:
                self.log_test("Error Handling - Past Date", True, {"expected_error": str(e)})
            
        except Exception as e:
            self.log_test("Error Handling Tests", False, {"error": str(e)})
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Amadeus Flight API Tests...")
        print("=" * 60)
        
        # Run test suites
        await self.test_authentication()
        await self.test_flight_offers_search()
        await self.test_flight_status()
        await self.test_cheapest_dates()
        await self.test_airport_search()
        await self.test_error_handling()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in self.failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("\n✅ All tests completed!")
        
        # Save detailed results to file
        with open("amadeus_test_results.json", "w") as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": len(self.failed_tests),
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        print("📄 Detailed results saved to: amadeus_test_results.json")

async def main():
    """Main test runner"""
    print("✈️ Amadeus Flight API Test Suite")
    print("Documentation: https://developers.amadeus.com/self-service/apis-docs/")
    print()
    
    # Check environment variables
    required_env_vars = ["AMADEUS_API_KEY", "AMADEUS_API_SECRET"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file or environment configuration.")
        return
    
    print("✅ Environment variables configured")
    print(f"API Key: {os.getenv('AMADEUS_API_KEY')[:10]}...")
    print()
    
    # Run tests
    tester = AmadeusAPITester()
    await tester.run_all_tests()

if __name__ == "__main__":
    # Environment variables are already loaded at the top of the file
    # Run the test suite
    asyncio.run(main())