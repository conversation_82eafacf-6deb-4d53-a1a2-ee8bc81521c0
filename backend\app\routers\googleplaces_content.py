# app/routers/googleplaces_content.py
from fastapi import APIRouter, HTTPException, Query, Response
from typing import Optional, List
import logging

from models.googleplaces_model import (
    PlaceDetailsResponse,
    PlaceDetailsRequest,
    PlacePhotoRequest,
    GooglePlacesError
)
from services.googleplaces_service import (
    get_place_details,
    get_place_photo,
    get_place_summary,
    get_place_photos_info
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/googleplaces", tags=["Google Places"])

@router.get("/place/details/{place_id}", response_model=PlaceDetailsResponse)
async def get_place_details_endpoint(
    place_id: str,
    fields: Optional[str] = Query(
        None,
        description="Comma-separated list of place data fields to return",
        example="place_id,name,formatted_address,geometry,rating,photos"
    ),
    language: str = Query("en", description="Language code for the response"),
    region: Optional[str] = Query(None, description="Region code for biasing results"),
    session_token: Optional[str] = Query(None, description="Session token for billing optimization")
):
    """
    Get detailed information about a specific place using Google Places API
    
    This endpoint retrieves comprehensive information about a place including:
    - Basic info (name, address, location)
    - Contact details (phone, website)
    - Ratings and reviews
    - Photos and opening hours
    - Business attributes
    
    **Cost Optimization**: Specify only needed fields to reduce API costs.
    """
    try:
        # Parse fields if provided
        fields_list = None
        if fields:
            fields_list = [field.strip() for field in fields.split(",")]
        
        # Get place details
        result = await get_place_details(
            place_id=place_id,
            fields=fields_list,
            language=language,
            region=region,
            session_token=session_token
        )
        
        logger.info(f"Successfully retrieved place details for: {result.result.name}")
        return result
        
    except GooglePlacesError as e:
        logger.error(f"Google Places API error: {e.message}")
        raise HTTPException(
            status_code=e.status_code or 400,
            detail=f"Google Places API error: {e.message}"
        )
    except ValueError as e:
        logger.error(f"Invalid request parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting place details: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/place/photo/{photo_reference}")
async def get_place_photo_endpoint(
    photo_reference: str,
    max_width: Optional[int] = Query(
        None,
        ge=1,
        le=1600,
        description="Maximum width of the photo (1-1600 pixels)"
    ),
    max_height: Optional[int] = Query(
        None,
        ge=1,
        le=1600,
        description="Maximum height of the photo (1-1600 pixels)"
    )
):
    """
    Get a place photo using Google Places API
    
    Returns the actual photo data as binary content. The photo will be resized
    to fit within the specified dimensions while maintaining aspect ratio.
    
    **Note**: At least one dimension (width or height) should be specified.
    If both are provided, width takes precedence.
    """
    try:
        # Get photo data
        photo_data = await get_place_photo(
            photo_reference=photo_reference,
            max_width=max_width,
            max_height=max_height
        )
        
        logger.info(f"Successfully retrieved photo: {len(photo_data)} bytes")
        
        # Return photo as binary response
        return Response(
            content=photo_data,
            media_type="image/jpeg",
            headers={
                "Content-Disposition": f"inline; filename=place_photo_{photo_reference[:10]}.jpg",
                "Cache-Control": "public, max-age=86400"  # Cache for 24 hours
            }
        )
        
    except GooglePlacesError as e:
        logger.error(f"Google Places API error: {e.message}")
        raise HTTPException(
            status_code=e.status_code or 400,
            detail=f"Google Places API error: {e.message}"
        )
    except ValueError as e:
        logger.error(f"Invalid request parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting place photo: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/place/summary/{place_id}")
async def get_place_summary_endpoint(place_id: str):
    """
    Get a summary of key place information with minimal API cost
    
    Returns essential place information including:
    - Name and address
    - Coordinates
    - Place types
    - Rating information
    - Business status
    - Current open/closed status
    """
    try:
        summary = await get_place_summary(place_id)
        
        logger.info(f"Successfully retrieved place summary for: {summary.get('name', 'Unknown')}")
        return {
            "status": "success",
            "data": summary
        }
        
    except GooglePlacesError as e:
        logger.error(f"Google Places API error: {e.message}")
        raise HTTPException(
            status_code=e.status_code or 400,
            detail=f"Google Places API error: {e.message}"
        )
    except ValueError as e:
        logger.error(f"Invalid request parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting place summary: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/place/photos/{place_id}")
async def get_place_photos_info_endpoint(place_id: str):
    """
    Get information about available photos for a place
    
    Returns a list of photo references and metadata without downloading
    the actual photo data. Use the photo references with the photo endpoint
    to retrieve actual images.
    """
    try:
        photos_info = await get_place_photos_info(place_id)
        
        logger.info(f"Successfully retrieved {len(photos_info)} photos info for place: {place_id}")
        return {
            "status": "success",
            "data": {
                "place_id": place_id,
                "photos_count": len(photos_info),
                "photos": photos_info
            }
        }
        
    except GooglePlacesError as e:
        logger.error(f"Google Places API error: {e.message}")
        raise HTTPException(
            status_code=e.status_code or 400,
            detail=f"Google Places API error: {e.message}"
        )
    except ValueError as e:
        logger.error(f"Invalid request parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting photos info: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/health")
async def health_check():
    """Health check endpoint for Google Places API integration"""
    try:
        # Simple validation that API key is configured
        from services.googleplaces_service import get_google_places_credentials
        api_key = get_google_places_credentials()
        
        return {
            "status": "healthy",
            "service": "Google Places API",
            "api_configured": bool(api_key),
            "endpoints": [
                "/place/details/{place_id}",
                "/place/photo/{photo_reference}",
                "/place/summary/{place_id}",
                "/place/photos/{place_id}"
            ]
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail=f"Service unavailable: {str(e)}"
        )