# app/routers/tomorrowio_content.py
from fastapi import APIRouter, Query, HTTPException
from typing import Optional, List
from datetime import datetime

from services.tomorrowio_service import (
    get_realtime_weather,
    validate_location,
    get_field_validation
)
from models.tomorrowio_model import (
    RealtimeWeatherResponse,
    Units,
    WeatherFields,
    TomorrowIOError,
    get_weather_description,
    calculate_comfort_score
)

router = APIRouter(prefix="/tomorrow", tags=["Tomorrow.io Weather"])

@router.get("/health")
async def health_check():
    """Health check endpoint for Tomorrow.io API integration"""
    return {
        "status": "healthy",
        "service": "Tomorrow.io Weather API",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "endpoints": ["realtime"]
    }

@router.get("/realtime")
async def get_realtime_weather_endpoint(
    location: str = Query(..., description="Location (city name or coordinates lat,lon)"),
    fields: Optional[str] = Query(None, description="Comma-separated list of weather fields"),
    units: Units = Query(Units.METRIC, description="Units system (metric/imperial)")
):
    """
    Get realtime weather data for a location
    
    - **location**: Location string (e.g., "London, UK" or "40.7128,-74.0060")
    - **fields**: Optional comma-separated weather fields (temperature, humidity, windSpeed, etc.)
    - **units**: Units system (metric or imperial)
    
    Returns current weather conditions including temperature, humidity, wind, precipitation, and more.
    """
    try:
        # Validate location
        if not validate_location(location):
            raise HTTPException(status_code=400, detail="Invalid location format")
        
        # Parse and validate fields
        field_list = None
        if fields:
            field_list = [field.strip() for field in fields.split(",")]
            validated_fields = get_field_validation(field_list)
            if not validated_fields:
                raise HTTPException(status_code=400, detail="No valid weather fields provided")
            field_list = validated_fields
        
        # Get weather data
        weather_data = await get_realtime_weather(
            location=location,
            fields=field_list,
            units=units.value
        )
        
        # Add weather description if weather code is available
        if "data" in weather_data and "values" in weather_data["data"]:
            values = weather_data["data"]["values"]
            if "weatherCode" in values and values["weatherCode"] is not None:
                values["weatherDescription"] = get_weather_description(values["weatherCode"])
            
            # Add comfort score if we have the required fields
            if all(field in values and values[field] is not None for field in ["temperature", "humidity", "windSpeed"]):
                comfort_score = calculate_comfort_score(
                    values["temperature"],
                    values["humidity"],
                    values["windSpeed"]
                )
                values["comfortScore"] = comfort_score
        
        return weather_data
        
    except TomorrowIOError as e:
        if e.status_code == 401:
            raise HTTPException(status_code=401, detail="Invalid API key")
        elif e.status_code == 404:
            raise HTTPException(status_code=404, detail="Location not found")
        elif e.status_code == 429:
            raise HTTPException(status_code=429, detail="API rate limit exceeded")
        else:
            raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/fields")
async def get_available_fields():
    """
    Get list of available weather fields for realtime weather
    
    Returns all available weather fields that can be requested from the Tomorrow.io API.
    """
    return {
        "available_fields": [
            {
                "field": field.value,
                "description": field.value.replace("_", " ").title()
            }
            for field in WeatherFields
        ],
        "default_fields": [
            "temperature", "temperatureApparent", "humidity", "windSpeed", 
            "windDirection", "precipitationIntensity", "precipitationProbability",
            "weatherCode", "cloudCover", "uvIndex", "visibility"
        ]
    }

@router.get("/weather-codes")
async def get_weather_codes():
    """
    Get weather code descriptions
    
    Returns mapping of weather codes to their descriptions.
    """
    from models.tomorrowio_model import WEATHER_CODE_DESCRIPTIONS
    
    return {
        "weather_codes": [
            {
                "code": code,
                "description": description
            }
            for code, description in WEATHER_CODE_DESCRIPTIONS.items()
        ]
    }