#!/usr/bin/env python3
"""
Test Application Startup API Validation
"""

import asyncio
import sys
import os

# Load environment variables FIRST
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

async def test_startup_validation():
    """Test the startup validation that runs when the app starts"""
    print("🚀 Testing Application Startup API Validation")
    print("=" * 50)
    
    try:
        # Import and setup logging first
        from app.core.logging_config import setup_logging
        setup_logging()
        
        # Import the API validator
        from app.core.api_validator import api_validator
        
        print("📋 Running startup API validation...")
        
        # This simulates what happens in main_enhanced.py startup event
        api_results = await api_validator.validate_all_apis()
        failed_apis = [api for api, status in api_results.items() if not status]
        
        print(f"\n📊 Validation Results:")
        for api_name, status in api_results.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {api_name}: {'Valid' if status else 'Invalid'}")
        
        print(f"\n📈 Summary:")
        total_apis = len(api_results)
        valid_apis = sum(1 for status in api_results.values() if status)
        success_rate = (valid_apis / total_apis) * 100
        
        print(f"   Total APIs: {total_apis}")
        print(f"   Valid APIs: {valid_apis}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        if failed_apis:
            print(f"\n⚠️ Failed APIs: {failed_apis}")
            print("   Note: This would be logged as a warning in the actual startup")
        else:
            print("\n🎉 All APIs validated successfully!")
            print("   This would be logged as success in the actual startup")
        
        # Test health endpoint data
        print(f"\n🏥 Health Check Data Preview:")
        print(f"   API Status: {api_results}")
        print(f"   Overall Health: {'healthy' if success_rate >= 80 else 'degraded' if success_rate >= 50 else 'unhealthy'}")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Startup validation test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_startup_validation())
    if success:
        print("\n✅ Startup validation test passed!")
    else:
        print("\n❌ Startup validation test failed!")