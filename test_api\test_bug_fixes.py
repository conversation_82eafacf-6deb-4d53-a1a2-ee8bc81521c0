#!/usr/bin/env python3
"""
Test script for BUG-011, BUG-013, and BUG-014 fixes
"""

import asyncio
import os
import sys
import time
from typing import Dict, Any

# Add the backend directory to the path
sys.path.append('.')

async def test_bug_011_hardcoded_values():
    """Test BUG-011: Hardcoded Default Values fix"""
    print("🧪 Testing BUG-011: Hardcoded Default Values...")
    
    try:
        from app.services.googleplaces_service import get_default_place_fields, get_summary_place_fields
        
        # Test default fields without environment variable
        default_fields = get_default_place_fields()
        print(f"✅ Default fields loaded: {len(default_fields)} fields")
        
        # Test with custom environment variable
        os.environ['GOOGLE_PLACES_DEFAULT_FIELDS'] = 'place_id,name,rating'
        custom_fields = get_default_place_fields()
        print(f"✅ Custom fields from env: {custom_fields}")
        
        # Test summary fields
        summary_fields = get_summary_place_fields()
        print(f"✅ Summary fields loaded: {len(summary_fields)} fields")
        
        # Clean up
        if 'GOOGLE_PLACES_DEFAULT_FIELDS' in os.environ:
            del os.environ['GOOGLE_PLACES_DEFAULT_FIELDS']
        
        print("✅ BUG-011 fix verified: Configurable field lists working")
        return True
        
    except Exception as e:
        print(f"❌ BUG-011 test failed: {e}")
        return False

async def test_bug_013_environment_defaults():
    """Test BUG-013: Missing Environment Defaults fix"""
    print("\n🧪 Testing BUG-013: Missing Environment Defaults...")
    
    try:
        from app.core.env_config import EnvironmentConfig, get_timeout_config, get_rate_limit_config
        
        # Test environment variable with default
        timeout = EnvironmentConfig.get_int_env_var('API_TIMEOUT_SECONDS', 30)
        print(f"✅ Timeout config with default: {timeout}s")
        
        # Test missing variable with default
        custom_var = EnvironmentConfig.get_env_var('NON_EXISTENT_VAR', 'default_value')
        print(f"✅ Missing var with default: {custom_var}")
        
        # Test timeout configuration
        timeout_config = get_timeout_config()
        print(f"✅ Timeout config: {timeout_config}")
        
        # Test rate limit configuration
        rate_config = get_rate_limit_config()
        print(f"✅ Rate limit config: {rate_config}")
        
        # Test development info
        dev_info = EnvironmentConfig.get_development_info()
        print(f"✅ Development defaults available: {len(dev_info['available_defaults'])}")
        
        print("✅ BUG-013 fix verified: Environment defaults working")
        return True
        
    except Exception as e:
        print(f"❌ BUG-013 test failed: {e}")
        return False

async def test_bug_014_async_error_handling():
    """Test BUG-014: Async Error Handling fix"""
    print("\n🧪 Testing BUG-014: Async Error Handling...")
    
    try:
        from app.core.async_error_handler import AsyncErrorHandler, async_api_call, api_error_context
        
        # Test async error handler decorator
        @AsyncErrorHandler.handle_async_errors(retry_count=2, fallback_value="fallback")
        async def test_function_with_retries():
            raise ConnectionError("Test error")
        
        result = await test_function_with_retries()
        print(f"✅ Retry with fallback: {result}")
        
        # Test timeout wrapper
        async def slow_function():
            await asyncio.sleep(2)
            return "completed"
        
        try:
            result = await AsyncErrorHandler.timeout_wrapper(
                slow_function(), 
                timeout_seconds=0.1, 
                fallback_value="timeout_fallback"
            )
            print(f"✅ Timeout handling: {result}")
        except asyncio.TimeoutError:
            print("✅ Timeout properly raised")
        
        # Test safe gather
        async def success_task():
            return "success"
        
        async def error_task():
            raise ValueError("test error")
        
        results = await AsyncErrorHandler.safe_gather(
            success_task(), 
            error_task(), 
            return_exceptions=True
        )
        print(f"✅ Safe gather results: success={isinstance(results[0], str)}, error={isinstance(results[1], Exception)}")
        
        # Test API error context
        async with api_error_context("Test API"):
            pass  # Should not raise
        print("✅ API error context working")
        
        print("✅ BUG-014 fix verified: Async error handling working")
        return True
        
    except Exception as e:
        print(f"❌ BUG-014 test failed: {e}")
        return False

async def test_integration():
    """Test integration of all fixes"""
    print("\n🧪 Testing Integration of All Fixes...")
    
    try:
        # Test that services can be imported with all fixes
        from app.services.googleplaces_service import get_google_places_credentials
        from app.services.flightapi_service import get_flightapi_credentials
        from app.core.env_config import EnvironmentConfig
        from app.core.async_error_handler import AsyncErrorHandler
        
        print("✅ All modules imported successfully")
        
        # Test environment configuration integration
        timeout_config = EnvironmentConfig.get_int_env_var('GOOGLE_PLACES_TIMEOUT_SECONDS', 30)
        print(f"✅ Google Places timeout: {timeout_config}s")
        
        # Test that error handling doesn't break normal operation
        @AsyncErrorHandler.handle_async_errors(retry_count=1)
        async def normal_operation():
            return {"status": "success", "data": "test"}
        
        result = await normal_operation()
        print(f"✅ Normal operation with error handling: {result['status']}")
        
        print("✅ Integration test passed: All fixes work together")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

async def main():
    """Run all bug fix tests"""
    print("🚀 Testing Bug Fixes: BUG-011, BUG-013, BUG-014")
    print("=" * 60)
    
    results = []
    
    # Test each bug fix
    results.append(await test_bug_011_hardcoded_values())
    results.append(await test_bug_013_environment_defaults())
    results.append(await test_bug_014_async_error_handling())
    results.append(await test_integration())
    
    print("\n" + "=" * 60)
    print("🎯 Bug Fix Test Results:")
    
    bug_names = ["BUG-011 (Hardcoded Values)", "BUG-013 (Environment Defaults)", 
                 "BUG-014 (Async Error Handling)", "Integration Test"]
    
    for i, (bug_name, result) in enumerate(zip(bug_names, results)):
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {bug_name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 Overall Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("🎉 All bug fixes verified successfully!")
        return True
    else:
        print("⚠️ Some tests failed - please review the fixes")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)