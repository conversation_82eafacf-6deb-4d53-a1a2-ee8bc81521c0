#!/usr/bin/env python3
"""
Test Amadeus API Key Fix
"""

import asyncio
import sys
import os

# Load environment variables FIRST
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'backend', '.env'))

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

async def test_amadeus_fix():
    """Test the Amadeus API key fix"""
    print("🔍 Testing Amadeus API Key Fix")
    print("=" * 40)
    
    # Check environment variables
    print("\n1. Checking Environment Variables...")
    amadues_key = os.getenv('AMADUES_API_KEY')  # Typo version
    amadeus_key = os.getenv('AMADEUS_API_KEY')  # Correct version
    amadeus_secret = os.getenv('AMADEUS_API_SECRET')
    
    print(f"   AMADUES_API_KEY (typo): {'✅ Found' if amadues_key else '❌ Not found'}")
    print(f"   AMADEUS_API_KEY (correct): {'✅ Found' if amadeus_key else '❌ Not found'}")
    print(f"   AMADEUS_API_SECRET: {'✅ Found' if amadeus_secret else '❌ Not found'}")
    
    if amadues_key:
        print(f"   Key preview: {amadues_key[:10]}...")
    
    # Test the service credentials function
    print("\n2. Testing Amadeus Service Credentials...")
    try:
        from app.services.amadeus_service import get_amadeus_credentials
        
        api_key, api_secret = get_amadeus_credentials()
        print(f"   ✅ Service can load credentials")
        print(f"   Key: {api_key[:10]}...")
        print(f"   Secret: {api_secret[:10]}...")
        
    except Exception as e:
        print(f"   ❌ Service credential loading failed: {e}")
    
    # Test the API validator
    print("\n3. Testing API Validator...")
    try:
        from app.core.api_validator import api_validator
        
        # Test just Amadeus validation
        is_valid = await api_validator._validate_amadeus()
        print(f"   {'✅' if is_valid else '❌'} Amadeus API validation: {'Valid' if is_valid else 'Invalid'}")
        
        if not is_valid:
            print("   🔍 Trying manual validation...")
            
            import aiohttp
            
            # Manual test with the credentials
            api_key = os.getenv('AMADUES_API_KEY')
            api_secret = os.getenv('AMADEUS_API_SECRET')
            
            if api_key and api_secret:
                async with aiohttp.ClientSession() as session:
                    data = {
                        'grant_type': 'client_credentials',
                        'client_id': api_key,
                        'client_secret': api_secret
                    }
                    
                    try:
                        async with session.post(
                            "https://test.api.amadeus.com/v1/security/oauth2/token",
                            data=data,
                            timeout=aiohttp.ClientTimeout(total=15)
                        ) as response:
                            response_text = await response.text()
                            print(f"   📊 Manual test - Status: {response.status}")
                            print(f"   📄 Response preview: {response_text[:200]}...")
                            
                            if response.status == 200:
                                print("   ✅ Manual validation successful!")
                            else:
                                print(f"   ❌ Manual validation failed: {response.status}")
                                
                    except Exception as e:
                        print(f"   ❌ Manual validation error: {e}")
            else:
                print("   ❌ No credentials found for manual test")
        
    except Exception as e:
        print(f"   ❌ API validator test failed: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 Amadeus API Key Investigation Complete")

if __name__ == "__main__":
    asyncio.run(test_amadeus_fix())