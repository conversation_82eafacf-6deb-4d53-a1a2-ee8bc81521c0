# app/routers/hotelbeds_content.py
from fastapi import APIRouter, Query, Path, HTTPException
from typing import Optional
from services.hotelbeds_service import (
    get_hotels_list,
    get_hotel_details,
    get_destinations,
    get_countries,
    get_accommodations,
    get_categories,
    get_chains,
    get_facilities
)
from models.hotelbeds_model import (
    HotelsResponse,
    HotelDetailsResponse,
    ErrorResponse
)

router = APIRouter(prefix="/hotelbeds/content", tags=["Hotelbeds Content API"])

@router.get("/hotels", response_model=HotelsResponse)
async def get_hotels(
    country_code: Optional[str] = Query(None, description="Country code (e.g., MY for Malaysia)"),
    state_code: Optional[str] = Query(None, description="State code"),
    destination_code: Optional[str] = Query(None, description="Destination code"),
    from_index: int = Query(1, ge=1, description="Starting index (minimum 1)"),
    to_index: int = Query(100, ge=1, le=1000, description="Ending index (maximum 1000)"),
    fields: str = Query("basic", description="Fields to return: basic, all"),
    language: str = Query("ENG", description="Language code (ENG, SPA, FRA, etc.)")
):
    """
    Get hotels list with optional filtering by country, state, or destination.
    Supports pagination with from/to parameters.
    """
    if to_index <= from_index:
        raise HTTPException(status_code=400, detail="to_index must be greater than from_index")
    
    if (to_index - from_index) > 1000:
        raise HTTPException(status_code=400, detail="Maximum 1000 hotels per request")
    
    return await get_hotels_list(
        country_code=country_code,
        state_code=state_code,
        destination_code=destination_code,
        from_index=from_index,
        to_index=to_index,
        fields=fields,
        language=language
    )

@router.get("/hotels/{hotel_code}", response_model=HotelDetailsResponse)
async def get_hotel_by_code(
    hotel_code: int = Path(..., description="Hotel code"),
    language: str = Query("ENG", description="Language code (ENG, SPA, FRA, etc.)")
):
    """
    Get detailed information for a specific hotel by its code.
    Returns comprehensive hotel data including facilities, rooms, images, etc.
    """
    return await get_hotel_details(hotel_code=hotel_code, language=language)

@router.get("/locations/destinations")
async def get_destinations_list(
    country_code: Optional[str] = Query(None, description="Filter by country code"),
    language: str = Query("ENG", description="Language code")
):
    """
    Get list of available destinations.
    Optionally filter by country code.
    """
    return await get_destinations(country_code=country_code, language=language)

@router.get("/locations/countries")
async def get_countries_list(
    language: str = Query("ENG", description="Language code")
):
    """
    Get list of available countries.
    """
    return await get_countries(language=language)

@router.get("/types/accommodations")
async def get_accommodation_types(
    language: str = Query("ENG", description="Language code")
):
    """
    Get list of accommodation types (hotel, apartment, etc.).
    """
    return await get_accommodations(language=language)

@router.get("/types/categories")
async def get_hotel_categories(
    language: str = Query("ENG", description="Language code")
):
    """
    Get list of hotel categories (star ratings, etc.).
    """
    return await get_categories(language=language)

@router.get("/types/chains")
async def get_hotel_chains(
    language: str = Query("ENG", description="Language code")
):
    """
    Get list of hotel chains.
    """
    return await get_chains(language=language)

@router.get("/types/facilities")
async def get_hotel_facilities(
    language: str = Query("ENG", description="Language code")
):
    """
    Get list of available hotel facilities and amenities.
    """
    return await get_facilities(language=language)
