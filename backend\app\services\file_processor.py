# services/file_processor.py
"""
File processing utilities for travel planning application
"""

import os
import mimetypes
from typing import Dict, Any, Optional, Union
import base64
import tempfile
import logging

logger = logging.getLogger(__name__)

class FileProcessor:
    """File processing utilities for handling uploaded files"""
    
    # Supported file types
    SUPPORTED_IMAGE_TYPES = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    SUPPORTED_AUDIO_TYPES = {'.mp3', '.wav', '.m4a', '.ogg', '.flac'}
    SUPPORTED_VIDEO_TYPES = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'}
    SUPPORTED_DOCUMENT_TYPES = {'.pdf', '.doc', '.docx', '.txt', '.rtf'}
    
    @classmethod
    async def process_file(cls, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Process uploaded file and extract relevant information
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            
        Returns:
            Dict containing processing results
        """
        try:
            file_info = cls.get_file_info(filename)
            file_type = file_info['type']
            
            result = {
                'processed': True,
                'filename': filename,
                'size': len(file_content),
                'type': file_type,
                'mime_type': file_info['mime_type'],
                'description': '',
                'metadata': {}
            }
            
            # Process based on file type
            if file_type == 'image':
                result.update(await cls._process_image(file_content, filename))
            elif file_type == 'audio':
                result.update(await cls._process_audio(file_content, filename))
            elif file_type == 'video':
                result.update(await cls._process_video(file_content, filename))
            elif file_type == 'document':
                result.update(await cls._process_document(file_content, filename))
            else:
                result['description'] = f"Unsupported file type: {file_type}"
                result['processed'] = False
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing file {filename}: {str(e)}")
            return {
                'processed': False,
                'filename': filename,
                'error': str(e),
                'description': f"Failed to process {filename}"
            }
    
    @classmethod
    def get_file_info(cls, filename: str) -> Dict[str, str]:
        """
        Get file type and MIME type information
        
        Args:
            filename: Name of the file
            
        Returns:
            Dict with file type and MIME type
        """
        file_ext = os.path.splitext(filename.lower())[1]
        mime_type, _ = mimetypes.guess_type(filename)
        
        if file_ext in cls.SUPPORTED_IMAGE_TYPES:
            file_type = 'image'
        elif file_ext in cls.SUPPORTED_AUDIO_TYPES:
            file_type = 'audio'
        elif file_ext in cls.SUPPORTED_VIDEO_TYPES:
            file_type = 'video'
        elif file_ext in cls.SUPPORTED_DOCUMENT_TYPES:
            file_type = 'document'
        else:
            file_type = 'unknown'
        
        return {
            'type': file_type,
            'extension': file_ext,
            'mime_type': mime_type or 'application/octet-stream'
        }
    
    @classmethod
    async def _process_image(cls, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process image files"""
        try:
            # Basic image processing
            # In a real implementation, you might use PIL/Pillow to extract metadata
            description = f"Image file: {filename}"
            
            # You could add image analysis here using AI services
            # For now, return basic information
            return {
                'description': description,
                'metadata': {
                    'analysis': 'Image uploaded for travel planning context',
                    'suggested_use': 'Visual reference for destination or activity preferences'
                }
            }
        except Exception as e:
            logger.error(f"Error processing image {filename}: {str(e)}")
            return {
                'description': f"Image processing failed for {filename}",
                'error': str(e)
            }
    
    @classmethod
    async def _process_audio(cls, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process audio files"""
        try:
            # Basic audio processing
            description = f"Audio file: {filename}"
            
            # In a real implementation, you might use speech-to-text services
            return {
                'description': description,
                'metadata': {
                    'analysis': 'Audio file uploaded for travel planning',
                    'suggested_use': 'Voice notes or audio descriptions for travel preferences'
                }
            }
        except Exception as e:
            logger.error(f"Error processing audio {filename}: {str(e)}")
            return {
                'description': f"Audio processing failed for {filename}",
                'error': str(e)
            }
    
    @classmethod
    async def _process_video(cls, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process video files"""
        try:
            # Basic video processing
            description = f"Video file: {filename}"
            
            # In a real implementation, you might extract frames or use video analysis
            return {
                'description': description,
                'metadata': {
                    'analysis': 'Video file uploaded for travel planning',
                    'suggested_use': 'Visual content showing destinations or activities of interest'
                }
            }
        except Exception as e:
            logger.error(f"Error processing video {filename}: {str(e)}")
            return {
                'description': f"Video processing failed for {filename}",
                'error': str(e)
            }
    
    @classmethod
    async def _process_document(cls, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process document files"""
        try:
            # Basic document processing
            description = f"Document file: {filename}"
            
            # In a real implementation, you might extract text content
            return {
                'description': description,
                'metadata': {
                    'analysis': 'Document uploaded for travel planning',
                    'suggested_use': 'Text content with travel information or preferences'
                }
            }
        except Exception as e:
            logger.error(f"Error processing document {filename}: {str(e)}")
            return {
                'description': f"Document processing failed for {filename}",
                'error': str(e)
            }
    
    @classmethod
    def is_supported_file(cls, filename: str) -> bool:
        """Check if file type is supported"""
        file_ext = os.path.splitext(filename.lower())[1]
        return file_ext in (
            cls.SUPPORTED_IMAGE_TYPES | 
            cls.SUPPORTED_AUDIO_TYPES | 
            cls.SUPPORTED_VIDEO_TYPES | 
            cls.SUPPORTED_DOCUMENT_TYPES
        )
    
    @classmethod
    def get_max_file_size(cls, file_type: str) -> int:
        """Get maximum file size for different file types (in bytes)"""
        size_limits = {
            'image': 10 * 1024 * 1024,    # 10MB
            'audio': 50 * 1024 * 1024,    # 50MB
            'video': 100 * 1024 * 1024,   # 100MB
            'document': 25 * 1024 * 1024, # 25MB
        }
        return size_limits.get(file_type, 10 * 1024 * 1024)  # Default 10MB