# app/core/logging_config.py
import logging
import sys
from typing import Dict, Any
from .api_config import APIConfig

def setup_logging() -> None:
    """Setup logging configuration based on environment"""
    
    log_level = APIConfig.get_log_level()
    environment = APIConfig.get_environment()
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Configure specific loggers
    loggers_config = {
        'uvicorn': 'INFO',
        'uvicorn.access': 'INFO' if environment.value == 'production' else 'DEBUG',
        'aiohttp': 'WARNING',
        'httpx': 'WARNING',
        'asyncio': 'WARNING'
    }
    
    for logger_name, level in loggers_config.items():
        logging.getLogger(logger_name).setLevel(getattr(logging, level))
    
    # Disable sensitive loggers in production
    if environment.value == 'production':
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)